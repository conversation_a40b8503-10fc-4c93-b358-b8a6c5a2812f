import {Suspense, useState, useEffect} from 'react';
import {Await, NavLink, useAsyncValue, useLocation} from '@remix-run/react';
import {
  type CartViewPayload,
  useAnalytics,
  useOptimisticCart,
} from '@shopify/hydrogen';
import type {HeaderQuery, CartApiQueryFragment} from 'storefrontapi.generated';
import {useAside} from '~/components/Aside';

// We'll use a direct path instead of import

interface HeaderProps {
  header: HeaderQuery;
  cart: Promise<CartApiQueryFragment | null>;
  isLoggedIn: Promise<boolean>;
  publicStoreDomain: string;
}

type Viewport = 'desktop' | 'mobile';

export function Header({
  header,
  isLoggedIn,
  cart,
  publicStoreDomain,
}: HeaderProps) {
  const {menu} = header;
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Reset scroll position when route changes
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-[#1a2333]/95 backdrop-blur-md shadow-lg py-4'
          : 'bg-[#1a2333] py-6'
      }`}
    >
      <div className="max-w-7xl mx-auto relative px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex-1 flex justify-start">
            <NavLink
              to="/"
              prefetch="intent"
              end
              className="inline-block relative"
            >
              <img
                src="/clear_logo.svg"
                alt="The Good Coffee Company"
                className="h-auto transition-opacity duration-300 hover:opacity-90"
                style={{
                  height: 'clamp(3.5rem, 8vw, 6rem)', /* Use rem instead of px for better scaling */
                  width: 'auto',
                  objectFit: 'contain'
                }}
              />
            </NavLink>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center justify-center flex-1">
            {menu && (
              <HeaderMenu
                menu={menu}
                viewport="desktop"
                primaryDomainUrl={header.shop.primaryDomain.url}
                publicStoreDomain={publicStoreDomain}
              />
            )}
          </div>

          {/* Right side icons */}
          <div className="flex items-center gap-5 flex-1 justify-end">
            <SearchToggle />

            <NavLink
              prefetch="intent"
              to="/account"
              style={activeLinkStyle}
              className="text-gray-200 hover:text-white transition-colors duration-300 relative hidden sm:block"
            >
              <span className="sr-only">Account</span>
              <svg style={{width: 'var(--icon-size-md)', height: 'var(--icon-size-md)'}} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <Suspense fallback={null}>
                <Await resolve={isLoggedIn} errorElement={null}>
                  {(isLoggedIn) => (
                    isLoggedIn && (
                      <span className="absolute -top-1 -right-1 bg-primary-500 w-2 h-2 rounded-full"></span>
                    )
                  )}
                </Await>
              </Suspense>
            </NavLink>

            <CartToggle cart={cart} />

            <div className="md:hidden">
              <HeaderMenuMobileToggle />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}

export function HeaderMenu({
  menu,
  primaryDomainUrl,
  viewport,
  publicStoreDomain,
}: {
  menu: HeaderProps['header']['menu'];
  primaryDomainUrl: HeaderProps['header']['shop']['primaryDomain']['url'];
  viewport: Viewport;
  publicStoreDomain: HeaderProps['publicStoreDomain'];
}) {
  const {close} = useAside();

  if (viewport === 'mobile') {
    return (
      <nav className="flex flex-col gap-4 p-6 bg-[#1a2333]" role="navigation">
        <div className="mb-6">
          <h3 className="text-white text-xl font-bold mb-4 pb-2 border-b border-gray-700">Navigation</h3>

          <NavLink
            end
            onClick={close}
            prefetch="intent"
            style={activeLinkStyle}
            to="/"
            className="flex items-center text-gray-200 hover:text-white transition-colors duration-300 text-lg font-medium py-2"
          >
            <svg style={{width: 'var(--icon-size-sm)', height: 'var(--icon-size-sm)'}} className="mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Home
          </NavLink>

          <NavLink
            onClick={close}
            prefetch="intent"
            style={activeLinkStyle}
            to="/buy"
            className="flex items-center text-gray-200 hover:text-white transition-colors duration-300 text-lg font-medium py-2"
          >
            <svg style={{width: 'var(--icon-size-sm)', height: 'var(--icon-size-sm)'}} className="mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            Shop
          </NavLink>

          <NavLink
            onClick={close}
            prefetch="intent"
            style={activeLinkStyle}
            to="/pages/about"
            className="flex items-center text-gray-200 hover:text-white transition-colors duration-300 text-lg font-medium py-2"
          >
            <svg style={{width: 'var(--icon-size-sm)', height: 'var(--icon-size-sm)'}} className="mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            About
          </NavLink>

          <NavLink
            onClick={close}
            prefetch="intent"
            style={activeLinkStyle}
            to="/pages/brewing-guides"
            className="flex items-center text-gray-200 hover:text-white transition-colors duration-300 text-lg font-medium py-2"
          >
            <svg style={{width: 'var(--icon-size-sm)', height: 'var(--icon-size-sm)'}} className="mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Brew
          </NavLink>

          {/* Brands link removed - temporarily deactivated */}
          {/* Affiliate link comes from menu items */}

          {/* Filter out "Buy Coffee", "About Us", "Brewing Guides", "Brewing Recipes", and "Brands" items in mobile menu */}
          {(menu || FALLBACK_HEADER_MENU).items
            .filter(item =>
              item.title !== 'Buy Coffee' &&
              item.title !== 'BUY COFFEE' &&
              item.title !== 'About' &&
              item.title !== 'ABOUT' &&
              item.title !== 'About Us' &&
              item.title !== 'ABOUT US' &&
              item.title !== 'Brewing Guides' &&
              item.title !== 'BREWING GUIDES' &&
              item.title !== 'Brewing Recipes' &&
              item.title !== 'BREWING RECIPES' &&
              item.title !== 'Brands' &&
              item.title !== 'BRANDS'
            )
            .map((item) => {
              if (!item.url) return null;

              // Redirect Shoutout affiliate links to our new affiliate page
              let url;
              if (item.url.includes('shoutout.global') || item.url.includes('shareasale.com')) {
                url = '/pages/affiliate';
              } else {
                url = item.url.includes('myshopify.com') ||
                     item.url.includes(publicStoreDomain) ||
                     item.url.includes(primaryDomainUrl)
                       ? new URL(item.url).pathname
                       : item.url;
              }

              return (
                <NavLink
                  className="flex items-center text-gray-200 hover:text-white transition-colors duration-300 text-lg font-medium py-2"
                  end
                  key={item.id}
                  onClick={close}
                  prefetch="intent"
                  style={activeLinkStyle}
                  to={url}
                >
                  <svg style={{width: 'var(--icon-size-sm)', height: 'var(--icon-size-sm)'}} className="mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {item.title}
                </NavLink>
              );
            })}
        </div>

        <div className="mt-auto pt-4 border-t border-gray-700">
          {/* Account Link */}
          <NavLink
            onClick={close}
            prefetch="intent"
            to="/account/login"
            className="flex items-center text-gray-200 hover:text-white transition-colors duration-300 text-lg font-medium py-2"
          >
            <svg style={{width: 'var(--icon-size-sm)', height: 'var(--icon-size-sm)'}} className="mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Sign In / Register
          </NavLink>
        </div>
      </nav>
    );
  }

  // Desktop menu
  return (
    <nav className="flex items-center justify-center space-x-8" role="navigation">
      <NavLink
        end
        prefetch="intent"
        style={activeLinkStyle}
        to="/"
        className="text-gray-200 hover:text-white transition-all duration-300 text-sm uppercase tracking-wider font-medium py-2 relative group"
      >
        HOME
        <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300 group-hover:w-full"></span>
      </NavLink>

      <NavLink
        prefetch="intent"
        style={activeLinkStyle}
        to="/buy"
        className="text-gray-200 hover:text-white transition-all duration-300 text-sm uppercase tracking-wider font-medium py-2 relative group"
      >
        SHOP
        <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300 group-hover:w-full"></span>
      </NavLink>

      <NavLink
        prefetch="intent"
        style={activeLinkStyle}
        to="/pages/about"
        className="text-gray-200 hover:text-white transition-all duration-300 text-sm uppercase tracking-wider font-medium py-2 relative group"
      >
        ABOUT
        <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300 group-hover:w-full"></span>
      </NavLink>

      <NavLink
        prefetch="intent"
        style={activeLinkStyle}
        to="/pages/brewing-guides"
        className="text-gray-200 hover:text-white transition-all duration-300 text-sm uppercase tracking-wider font-medium py-2 relative group"
      >
        BREW
        <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300 group-hover:w-full"></span>
      </NavLink>

      {/* Brands link removed - temporarily deactivated */}
      {/* Affiliate link comes from menu items */}

      {(menu || FALLBACK_HEADER_MENU).items
        .filter(item =>
          item.title !== 'Buy Coffee' &&
          item.title !== 'BUY COFFEE' &&
          item.title !== 'About' &&
          item.title !== 'ABOUT' &&
          item.title !== 'About Us' &&
          item.title !== 'ABOUT US' &&
          item.title !== 'Brewing Guides' &&
          item.title !== 'BREWING GUIDES' &&
          item.title !== 'Brewing Recipes' &&
          item.title !== 'BREWING RECIPES' &&
          item.title !== 'Brands' &&
          item.title !== 'BRANDS'
        )
        .map((item) => {
          if (!item.url) return null;

          // Redirect Shoutout affiliate links to our new affiliate page
          let url;
          if (item.url.includes('shoutout.global') || item.url.includes('shareasale.com')) {
            url = '/pages/affiliate';
          } else {
            url = item.url.includes('myshopify.com') ||
                 item.url.includes(publicStoreDomain) ||
                 item.url.includes(primaryDomainUrl)
                   ? new URL(item.url).pathname
                   : item.url;
          }

          return (
            <NavLink
              className="text-gray-200 hover:text-white transition-all duration-300 text-sm uppercase tracking-wider font-medium py-2 relative group"
              end
              key={item.id}
              onClick={close}
              prefetch="intent"
              style={activeLinkStyle}
              to={url}
            >
              {item.title.toUpperCase()}
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300 group-hover:w-full"></span>
            </NavLink>
          );
        })}
    </nav>
  );
}



function HeaderMenuMobileToggle() {
  const {open} = useAside();
  return (
    <button
      className="text-gray-200 hover:text-white transition-all duration-300 transform hover:scale-110 p-1 rounded-md hover:bg-white/10"
      onClick={() => open('mobile')}
      aria-label="Open menu"
    >
      <span className="sr-only">Menu</span>
      <svg style={{width: 'var(--icon-size-md)', height: 'var(--icon-size-md)'}} fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
      </svg>
    </button>
  );
}

function SearchToggle() {
  const {open} = useAside();
  return (
    <button
      className="text-gray-200 hover:text-white transition-all duration-300 cursor-pointer transform hover:scale-110 p-1 rounded-md hover:bg-white/10"
      onClick={() => open('search')}
      aria-label="Search"
    >
      <span className="sr-only">Search</span>
      <svg style={{width: 'var(--icon-size-md)', height: 'var(--icon-size-md)'}} fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    </button>
  );
}

function CartBadge({count}: {count: number | null}) {
  const {publish, shop, cart, prevCart} = useAnalytics();
  const {open} = useAside();

  return (
    <button
      onClick={() => {
        open('cart');
        publish('cart_viewed', {
          cart,
          prevCart,
          shop,
          url: window.location.href || '',
        } as CartViewPayload);
      }}
      className="text-gray-200 hover:text-white transition-all duration-300 relative cursor-pointer transform hover:scale-110 p-1 rounded-md hover:bg-white/10"
      aria-label={`Cart with ${count || 0} items`}
    >
      <span className="sr-only">Cart</span>
      <svg style={{width: 'var(--icon-size-md)', height: 'var(--icon-size-md)'}} fill="none" viewBox="0 0 24 24" stroke="white">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
      </svg>
      {count !== null && count > 0 && (
        <span
          className="absolute -top-2 -right-2 bg-primary-500 text-white text-xs font-bold rounded-full flex items-center justify-center shadow-md transition-all duration-300 animate-pulse-subtle"
          style={{width: 'var(--icon-size-sm)', height: 'var(--icon-size-sm)'}}
        >
          {count}
        </span>
      )}
    </button>
  );
}

function CartToggle({cart}: Pick<HeaderProps, 'cart'>) {
  return (
    <Suspense fallback={<CartBadge count={null} />}>
      <Await resolve={cart}>
        <CartBanner />
      </Await>
    </Suspense>
  );
}

function CartBanner() {
  const originalCart = useAsyncValue() as CartApiQueryFragment | null;
  const cart = useOptimisticCart(originalCart);
  return <CartBadge count={cart?.totalQuantity ?? 0} />;
}

const FALLBACK_HEADER_MENU = {
  id: 'gid://shopify/Menu/199655587896',
  items: [
    {
      id: 'gid://shopify/MenuItem/461609500728',
      resourceId: null,
      tags: [],
      title: 'Collections',
      type: 'HTTP',
      url: '/collections',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461609533496',
      resourceId: null,
      tags: [],
      title: 'Blog',
      type: 'HTTP',
      url: '/blogs/journal',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461609566264',
      resourceId: null,
      tags: [],
      title: 'Policies',
      type: 'HTTP',
      url: '/policies',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461609599032',
      resourceId: 'gid://shopify/Page/92591030328',
      tags: [],
      title: 'About',
      type: 'PAGE',
      url: '/pages/about',
      items: [],
    },
  ],
};

function activeLinkStyle({
  isActive,
  isPending,
}: {
  isActive: boolean;
  isPending: boolean;
}) {
  return {
    fontWeight: isActive ? 'bold' : undefined,
    // Using light grey for pending and white for active
    color: isPending ? 'rgb(209, 213, 219)' : isActive ? 'rgb(255, 255, 255)' : 'rgb(229, 231, 235)',
  };
}

