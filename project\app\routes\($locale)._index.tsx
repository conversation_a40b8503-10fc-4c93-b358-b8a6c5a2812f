import {type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {Await, useLoaderData, Link, type MetaFunction} from '@remix-run/react';
import {Suspense, useState, useEffect} from 'react';
import {Image, Money} from '@shopify/hydrogen';
import type {
  RecommendedProductsQuery,
} from 'storefrontapi.generated';
import {EnhancedProductGrid} from '~/components/EnhancedProductGrid';
import {SubscriptionBanner} from '~/components/SubscriptionBanner';
import {VideoIntro} from '~/components/VideoIntro';

// Custom hook for window size
function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
  });

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
      });
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
      handleResize();
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  return windowSize;
}

export const meta: MetaFunction = () => {
  return [{title: 'The Good Coffee Company | Home'}];
};

export async function loader({context}: LoaderFunctionArgs) {
  const {storefront} = context;

  const recommendedProducts = await storefront.query(RECOMMENDED_PRODUCTS_QUERY);

  return {
    recommendedProducts,
  };
}

export default function Homepage() {
  const {recommendedProducts} = useLoaderData<typeof loader>();
  const [showVideoIntro, setShowVideoIntro] = useState(true);
  const [hasSeenVideo, setHasSeenVideo] = useState(false);
  const [isVideoFadingOut, setIsVideoFadingOut] = useState(false);
  const { width } = useWindowSize();

  useEffect(() => {
    // Check if user has already seen the video in this session
    const videoSeen = sessionStorage.getItem('videoIntroSeen');
    if (videoSeen) {
      setShowVideoIntro(false);
      setHasSeenVideo(true);
    }
  }, []);

  const handleVideoEnd = () => {
    setIsVideoFadingOut(true);
    // Wait for video fade out, then hide video and show homepage
    setTimeout(() => {
      setShowVideoIntro(false);
      setHasSeenVideo(true);
      sessionStorage.setItem('videoIntroSeen', 'true');
    }, 1000);
  };

  const handleSkipVideo = () => {
    setIsVideoFadingOut(true);
    // Wait for video fade out, then hide video and show homepage
    setTimeout(() => {
      setShowVideoIntro(false);
      setHasSeenVideo(true);
      sessionStorage.setItem('videoIntroSeen', 'true');
    }, 1000);
  };

  return (
    <div className="relative">
      {/* Video Intro Overlay */}
      {showVideoIntro && (
        <VideoIntro
          onVideoEnd={handleVideoEnd}
          onSkip={handleSkipVideo}
        />
      )}

      {/* Main Content */}
      <div className={`space-y-16 pb-16 transition-opacity duration-1000 ${
        showVideoIntro && !isVideoFadingOut ? 'opacity-0' : 'opacity-100'
      }`}>
      {/* Hero Section - Updated with same format but enhanced styling */}
      <div className="hero-section relative h-[90vh] min-h-[600px] flex items-center justify-center overflow-hidden">
        {/* Background image with parallax effect */}
        <div
          className="absolute inset-0 z-0"
          style={{
            margin: '-2rem',
            transform: 'scale(1.1)'
          }}
        >
          <div
            className="absolute inset-0 transform transition-transform duration-10000 animate-subtle-zoom"
            style={{
              transform: 'scale(1.2)',
              transformOrigin: 'center center'
            }}
          >
            <img
              src="/medium_pretty_bag.webp"
              alt="Coffee bag packaging"
              className="w-full h-full object-cover"
            />
          </div>
          {/* Overlay with gradient */}
          <div className="absolute inset-0 bg-gradient-to-b from-black/80 via-black/60 to-black/80" />

          {/* Grain texture for added depth */}
          <div className="absolute inset-0 opacity-20 mix-blend-overlay bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIiB4PSIwIiB5PSIwIj48ZmVUdXJidWxlbmNlIGJhc2VGcmVxdWVuY3k9Ii43NSIgc3RpdGNoVGlsZXM9InN0aXRjaCIgdHlwZT0iZnJhY3RhbE5vaXNlIi8+PGZlQ29sb3JNYXRyaXggdHlwZT0ic2F0dXJhdGUiIHZhbHVlcz0iMCIvPjwvZmlsdGVyPjxwYXRoIGQ9Ik0wIDBoMzAwdjMwMEgweiIgZmlsdGVyPSJ1cmwoI2EpIiBvcGFjaXR5PSIuMDUiLz48L3N2Zz4=')]" />
        </div>

        {/* Content with staggered animation */}
        <div className="relative z-10 text-center text-white h-full flex flex-col justify-center max-w-full mx-auto px-4 animate-fade">
          <h1
            className="font-black leading-none tracking-tight animate-slide-up mb-8 sm:mb-12"
            style={{
              animationDelay: '200ms',
              fontSize: width < 640 ? 'clamp(2.5rem, 12vw, 4rem)' : 'clamp(2rem, 7.5vw, 10rem)',
              lineHeight: width < 640 ? '1.1' : '0.9'
            }}
          >
            Good Days Start Here
          </h1>

          <div className="hero-buttons flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 animate-slide-up pt-6 sm:pt-12 mt-4 sm:mt-8 max-w-sm sm:max-w-none mx-auto" style={{animationDelay: '600ms'}}>
            <Link
              to="/pages/about"
              className="inline-block bg-white rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 hover:scale-105 shadow-lg text-center"
              style={{
                padding: width < 640 ? '1rem 2rem' : '1rem 2rem',
                minWidth: width < 640 ? '200px' : 'auto',
                width: width < 640 ? '100%' : 'auto',
                fontSize: width < 640 ? '1rem' : '1rem'
              }}
            >
              <span className="text-black">Our Story</span>
            </Link>
            <Link
              to="/buy"
              className="inline-block bg-white rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 hover:scale-105 shadow-lg text-center"
              style={{
                padding: width < 640 ? '1rem 2rem' : '1rem 2rem',
                minWidth: width < 640 ? '200px' : 'auto',
                width: width < 640 ? '100%' : 'auto',
                fontSize: width < 640 ? '1rem' : '1rem'
              }}
            >
              <span className="text-black">Shop Coffee</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Subscription Banner */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <SubscriptionBanner />
      </div>

      {/* Featured Products Section - Redesigned without product selection */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 bg-gradient-to-b from-white to-gray-50">
        <div className="flex flex-col items-center text-center mb-16">
          <span className="text-white font-semibold text-sm uppercase tracking-wider bg-[#1e3a8a] px-4 py-1 rounded-full shadow-sm mb-3">Our Selection</span>
          <h2 className="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4">Exceptional Coffee Selection</h2>
          <div className="w-24 h-1 bg-[#1e3a8a] rounded mb-6"></div>
          <p className="text-gray-800 max-w-2xl mb-8 text-lg">
            Each of our coffees tells a unique story, from the high-altitude farms where they're grown to the careful roasting process that brings out their distinctive character.
          </p>
        </div>

        <RecommendedProducts products={recommendedProducts} />

        <div className="flex justify-center mt-12">
          <Link
            to="/buy"
            className="inline-flex items-center px-8 py-4 bg-[#d1d5db] rounded-lg hover:bg-[#9ca3af] transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
          >
            <span className="text-black">Explore All Coffees</span>
            <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </div>
      </div>

      {/* New Text Section inspired by thegoodcoffeeco.com */}
      <div className="py-24 bg-gradient-to-b from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-16 items-center">
            <div className="space-y-6">
              <div>
                <span className="text-white font-semibold text-sm uppercase tracking-wider bg-[#1e3a8a] px-4 py-1 rounded-full shadow-sm inline-block mb-3">Our Philosophy</span>
                <h2 className="text-3xl md:text-4xl font-extrabold text-gray-900">Coffee with Purpose</h2>
              </div>

              <div className="w-24 h-1 bg-[#1e3a8a] rounded"></div>

              <p className="text-gray-800 text-lg leading-relaxed">
                At The Good Coffee Company, we believe that exceptional coffee is more than just a beverage—it's a journey from seed to cup that respects both people and planet. We work directly with small-scale farmers who share our commitment to sustainable practices and ethical production.
              </p>

              <p className="text-gray-800 text-lg leading-relaxed">
                Every bean we source is carefully selected for its unique character and quality, then roasted in small batches to bring out its distinctive flavor profile. The result is fresh, low acid, mold free coffee that not only tastes extraordinary but also makes a positive impact on communities around the world.
              </p>

              <div className="pt-4">
                <Link
                  to="/buy"
                  className="inline-flex items-center px-6 py-3 bg-[#d1d5db] rounded-lg font-medium hover:bg-[#9ca3af] transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                >
                  <span className="text-black">Discover Our Coffee</span>
                  <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Link>
              </div>
            </div>

            <div className="relative">
              <div className="absolute -top-6 -left-6 w-24 h-24 bg-primary-100 rounded-lg z-0"></div>
              <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-primary-200 rounded-lg z-0"></div>
              <img
                src="/table_coffee_bag.webp"
                alt="Coffee bag on table"
                className="rounded-lg shadow-xl relative z-10 transform transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Quality Commitment Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-28 bg-gradient-to-b from-white to-[#f1f5f9]">
        <div className="grid md:grid-cols-2 gap-16 items-center">
          <div className="order-2 md:order-1 space-y-6">
            <div>
              <span className="text-white font-semibold text-sm uppercase tracking-wider bg-[#1e3a8a] px-4 py-1 rounded-full shadow-sm inline-block mb-3">Our Commitment</span>
              <h2 className="text-3xl md:text-4xl font-extrabold text-gray-900">Quality in Every Cup</h2>
            </div>

            <div className="w-24 h-1 bg-[#1e3a8a] rounded"></div>

            <p className="text-gray-800 text-lg leading-relaxed">
              We believe that truly exceptional coffee requires attention to detail at every stage. From selecting the finest beans to precise roasting profiles, we're committed to quality in everything we do.
            </p>

            <div className="grid sm:grid-cols-2 gap-6 py-4">
              <div className="flex items-start">
                <div className="flex-shrink-0 bg-primary-100 rounded-lg p-3">
                  <svg className="w-6 h-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Small Batch Roasting</h3>
                  <p className="mt-1 text-gray-600">Ensuring freshness and flavor in every bag.</p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="flex-shrink-0 bg-primary-100 rounded-lg p-3">
                  <svg className="w-6 h-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Sustainable Practices</h3>
                  <p className="mt-1 text-gray-600">Environmentally conscious from farm to cup.</p>
                </div>
              </div>
            </div>

            <Link
              to="/pages/brewing-guides"
              className="inline-flex items-center px-6 py-3 bg-[#d1d5db] text-gray-700 rounded-lg font-medium hover:bg-[#9ca3af] transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
            >
              Brewing Guides
              <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </Link>
          </div>

          <div className="order-1 md:order-2 relative">
            <div className="absolute -top-6 -right-6 w-24 h-24 bg-primary-100 rounded-lg z-0"></div>
            <div className="absolute -bottom-6 -left-6 w-24 h-24 bg-primary-200 rounded-lg z-0"></div>
            <img
              src="/brewing_3.webp"
              alt="Coffee brewing process"
              className="rounded-lg shadow-xl relative z-10 transform transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl"
            />
          </div>
        </div>
      </div>

      {/* Testimonials Section - Enhanced with modern design */}
      <div className="bg-gradient-to-b from-[#f1f5f9] to-[#e2e8f0] py-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col items-center text-center mb-16">
            <span className="text-white font-semibold text-sm uppercase tracking-wider bg-[#1e3a8a] px-4 py-1 rounded-full shadow-sm mb-3">Customer Stories</span>
            <h2 className="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4">Coffee Worth Talking About</h2>
            <div className="w-24 h-1 bg-[#1e3a8a] rounded mb-6"></div>
            <p className="text-gray-800 max-w-2xl text-lg">
              Our customers are passionate about great coffee. Here's what they have to say about their experience with The Good Coffee Company.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Testimonial 1 - Enhanced with modern styling and better contrast */}
            <div className="bg-white rounded-xl shadow-xl p-8 transform transition-all duration-300 hover:scale-105 hover:shadow-2xl border border-gray-100">
              <div className="flex items-center mb-6">
                <div className="h-16 w-16 rounded-full bg-[#1e3a8a] flex items-center justify-center shadow-md">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804 .167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h3 className="font-bold text-lg text-gray-900">Sarah Johnson</h3>
                  <div className="flex text-yellow-500 mt-1">
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                  </div>
                </div>
              </div>
              <p className="text-gray-800 italic leading-relaxed text-lg">
                "The Colombia Medium Roast is absolutely incredible. The flavor notes are exactly as described, and the aroma fills my kitchen every morning. I've tried many coffee subscriptions, but The Good Coffee Company is by far the best."
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <span className="text-sm bg-[#e0e7ff] text-[#1e3a8a] font-bold py-1 px-3 rounded-full">Colombia Medium Roast</span>
              </div>
            </div>

            {/* Testimonial 2 - Enhanced with modern styling and better contrast */}
            <div className="bg-white rounded-xl shadow-xl p-8 transform transition-all duration-300 hover:scale-105 hover:shadow-2xl border border-gray-100">
              <div className="flex items-center mb-6">
                <div className="h-16 w-16 rounded-full bg-[#1e3a8a] flex items-center justify-center shadow-md">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804 .167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804 .167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h3 className="font-bold text-lg text-gray-900">Michael Rodriguez</h3>
                  <div className="flex text-yellow-500 mt-1">
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                  </div>
                </div>
              </div>
              <p className="text-gray-800 italic leading-relaxed text-lg">
                "As a coffee enthusiast, I appreciate the transparency about where the beans come from. The Nicaragua Medium Roast has become my go-to for my morning pour-over. The shipping is always fast, and the beans are always fresh."
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <span className="text-sm bg-[#e0e7ff] text-[#1e3a8a] font-bold py-1 px-3 rounded-full">Nicaragua Medium Roast</span>
              </div>
            </div>

            {/* Testimonial 3 - Enhanced with modern styling and better contrast */}
            <div className="bg-white rounded-xl shadow-xl p-8 transform transition-all duration-300 hover:scale-105 hover:shadow-2xl border border-gray-100">
              <div className="flex items-center mb-6">
                <div className="h-16 w-16 rounded-full bg-[#1e3a8a] flex items-center justify-center shadow-md">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804 .167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804 .167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h3 className="font-bold text-lg text-gray-900">Emily Chen</h3>
                  <div className="flex text-yellow-500 mt-1">
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                  </div>
                </div>
              </div>
              <p className="text-gray-800 italic leading-relaxed text-lg">
                "I love that I can learn about the farmers who grow the coffee I'm drinking. The Brazil Dark Roast is perfect for my espresso machine. The customer service is also exceptional - they helped me choose the perfect grind for my brewing method."
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <span className="text-sm bg-[#e0e7ff] text-[#1e3a8a] font-bold py-1 px-3 rounded-full">Brazil Dark Roast</span>
              </div>
            </div>
          </div>

          {/* Call to action - Enhanced with better styling */}
          <div className="mt-20 text-center">
            <Link
              to="/buy"
              className="inline-flex items-center px-10 py-5 bg-[#d1d5db] rounded-lg font-bold text-lg hover:bg-[#9ca3af] transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
            >
              <span className="text-black">Try Our Coffee</span>
              <svg className="ml-3 w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </Link>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
}

function RecommendedProducts({
  products,
}: {
  products: Promise<RecommendedProductsQuery>;
}) {
  return (
    <div className="recommended-products">
      <Suspense fallback={
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      }>
        <Await resolve={products}>
          {(response) => (
            <EnhancedProductGrid
              products={response?.products?.nodes || []}
              columnsDesktop={4}
              columnsMobile={2}
              enableFiltering={true}
              enableSorting={true}
              highlightNewest={true}
              className="mt-6"
            />
          )}
        </Await>
      </Suspense>
    </div>
  );
}

const RECOMMENDED_PRODUCTS_QUERY = `#graphql
  fragment RecommendedProduct on Product {
    id
    title
    handle
    vendor
    availableForSale
    priceRange {
      minVariantPrice {
        amount
        currencyCode
      }
    }
    variants(first: 1) {
      nodes {
        id
        availableForSale
        price {
          amount
          currencyCode
        }
        compareAtPrice {
          amount
          currencyCode
        }
      }
    }
    featuredImage {
      id
      url
      altText
      width
      height
    }
  }
  query RecommendedProducts ($country: CountryCode, $language: LanguageCode)
    @inContext(country: $country, language: $language) {
    products(first: 8, sortKey: UPDATED_AT, reverse: true) {
      nodes {
        ...RecommendedProduct
      }
    }
  }
` as const;