import {redirect, type LoaderFunctionArgs} from '@shopify/remix-oxygen';

export async function loader({context, request}: LoaderFunctionArgs) {
  try {
    // Handle the authorization from Shopify
    const result = await context.customerAccount.authorize();

    // If authorization is successful, redirect to home page
    // You can change this to redirect to account page if preferred
    return redirect('/');
  } catch (error) {
    // If authorization fails, redirect to login with error
    return redirect('/account/login?error=authorization_failed');
  }
}
