import {useAnalytics} from '@shopify/hydrogen';
import {useEffect, useState} from 'react';

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

/**
 * Google Analytics 4 integration component
 * This component loads GA4 post-hydration and subscribes to Shopify Analytics events
 */
export function GoogleAnalytics() {
  const {subscribe, register, ready} = useAnalytics();
  const [isGALoaded, setIsGALoaded] = useState(false);

  useEffect(() => {
    // Load Google Analytics script post-hydration to avoid performance issues
    const loadGoogleAnalytics = () => {
      // Check if already loaded
      if (window.gtag) {
        setIsGALoaded(true);
        return;
      }

      // Create and load the GA script
      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://www.googletagmanager.com/gtag/js?id=G-M14J3Y9HZ3';

      script.onload = () => {
        // Initialize dataLayer and gtag function
        window.dataLayer = window.dataLayer || [];
        function gtag(...args: any[]) {
          window.dataLayer.push(args);
        }
        window.gtag = gtag;

        // Configure Google Analytics
        gtag('js', new Date());
        gtag('config', 'G-M14J3Y9HZ3', {
          // Essential for ad attribution
          send_page_view: true,
          allow_google_signals: true,
          allow_ad_personalization_signals: true,
        });

        setIsGALoaded(true);
        console.log('Google Analytics loaded successfully');
      };

      script.onerror = () => {
        console.error('Failed to load Google Analytics');
      };

      document.head.appendChild(script);
    };

    // Load GA after a short delay to ensure React hydration is complete
    const timer = setTimeout(loadGoogleAnalytics, 100);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Only register and subscribe after GA is loaded
    if (!isGALoaded) return;

    // Register this analytics integration
    register('Google Analytics 4');

    // Subscribe to page view events
    subscribe('page_viewed', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'page_view', {
          page_title: data.pageTitle || document.title,
          page_location: data.url || window.location.href,
          page_path: data.path || window.location.pathname,
        });
      }
    });

    // Subscribe to product view events
    subscribe('product_viewed', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        const product = data.products?.[0];
        if (product) {
          window.gtag('event', 'view_item', {
            currency: product.price?.currencyCode || 'USD',
            value: parseFloat(product.price?.amount || '0'),
            item_id: product.id,
            item_name: product.title,
            item_category: product.productType,
            item_variant: product.variant?.title,
            item_brand: product.vendor,
          });
        }
      }
    });

    // Subscribe to collection view events
    subscribe('collection_viewed', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'view_item_list', {
          item_list_id: data.collection?.id,
          item_list_name: data.collection?.title,
        });
      }
    });

    // Subscribe to cart view events
    subscribe('cart_viewed', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        const cart = data.cart;
        if (cart) {
          window.gtag('event', 'view_cart', {
            currency: cart.cost?.totalAmount?.currencyCode || 'USD',
            value: parseFloat(cart.cost?.totalAmount?.amount || '0'),
            items: cart.lines?.nodes?.map((line: any) => ({
              item_id: line.merchandise?.id,
              item_name: line.merchandise?.product?.title,
              item_category: line.merchandise?.product?.productType,
              item_variant: line.merchandise?.title,
              item_brand: line.merchandise?.product?.vendor,
              price: parseFloat(line.cost?.totalAmount?.amount || '0'),
              quantity: line.quantity,
            })) || [],
          });
        }
      }
    });

    // Subscribe to search events
    subscribe('search_submitted', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'search', {
          search_term: data.searchTerm,
        });
      }
    });

    // Subscribe to add to cart events
    subscribe('product_added_to_cart', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        const cartLine = data.cartLine;
        if (cartLine) {
          window.gtag('event', 'add_to_cart', {
            currency: cartLine.cost?.totalAmount?.currencyCode || 'USD',
            value: parseFloat(cartLine.cost?.totalAmount?.amount || '0'),
            items: [{
              item_id: cartLine.merchandise?.id,
              item_name: cartLine.merchandise?.product?.title,
              item_category: cartLine.merchandise?.product?.productType,
              item_variant: cartLine.merchandise?.title,
              item_brand: cartLine.merchandise?.product?.vendor,
              price: parseFloat(cartLine.cost?.totalAmount?.amount || '0'),
              quantity: cartLine.quantity,
            }],
          });
        }
      }
    });

    // Subscribe to remove from cart events
    subscribe('product_removed_from_cart', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        const cartLine = data.cartLine;
        if (cartLine) {
          window.gtag('event', 'remove_from_cart', {
            currency: cartLine.cost?.totalAmount?.currencyCode || 'USD',
            value: parseFloat(cartLine.cost?.totalAmount?.amount || '0'),
            items: [{
              item_id: cartLine.merchandise?.id,
              item_name: cartLine.merchandise?.product?.title,
              item_category: cartLine.merchandise?.product?.productType,
              item_variant: cartLine.merchandise?.title,
              item_brand: cartLine.merchandise?.product?.vendor,
              price: parseFloat(cartLine.cost?.totalAmount?.amount || '0'),
              quantity: cartLine.quantity,
            }],
          });
        }
      }
    });

    // Subscribe to checkout started events
    subscribe('checkout_started', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        const cart = data.cart;
        if (cart) {
          window.gtag('event', 'begin_checkout', {
            currency: cart.cost?.totalAmount?.currencyCode || 'USD',
            value: parseFloat(cart.cost?.totalAmount?.amount || '0'),
            items: cart.lines?.nodes?.map((line: any) => ({
              item_id: line.merchandise?.id,
              item_name: line.merchandise?.product?.title,
              item_category: line.merchandise?.product?.productType,
              item_variant: line.merchandise?.title,
              item_brand: line.merchandise?.product?.vendor,
              price: parseFloat(line.cost?.totalAmount?.amount || '0'),
              quantity: line.quantity,
            })) || [],
          });
        }
      }
    });

    // Subscribe to purchase events
    subscribe('checkout_completed', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        const checkout = data.checkout;
        if (checkout) {
          window.gtag('event', 'purchase', {
            transaction_id: checkout.order?.id,
            currency: checkout.totalPrice?.currencyCode || 'USD',
            value: parseFloat(checkout.totalPrice?.amount || '0'),
            tax: parseFloat(checkout.totalTax?.amount || '0'),
            shipping: parseFloat(checkout.shippingLine?.price?.amount || '0'),
            items: checkout.lineItems?.map((line: any) => ({
              item_id: line.variant?.id,
              item_name: line.title,
              item_category: line.variant?.product?.productType,
              item_variant: line.variant?.title,
              item_brand: line.variant?.product?.vendor,
              price: parseFloat(line.variant?.price?.amount || '0'),
              quantity: line.quantity,
            })) || [],
          });
        }
      }
    });

    // Notify that this integration is ready
    ready();

    // Cleanup function
    return () => {
      // No cleanup needed for analytics subscriptions
    };
  }, [isGALoaded]); // Re-run when GA loading state changes

  return null; // This component doesn't render anything
}
