import {type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {useLoaderData, Link} from '@remix-run/react';

export async function loader({context}: LoaderFunctionArgs) {
  const {storefront} = context;
  
  // Fetch all products
  const {products} = await storefront.query(ALL_PRODUCTS_QUERY);
  
  return {
    products: products.nodes,
  };
}

export default function ProductList() {
  const {products} = useLoaderData<typeof loader>();
  
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <h1 className="text-2xl font-bold mb-6">All Products (with Handles)</h1>
      <p className="mb-8 text-gray-600">
        This is a utility page to help you find the correct product handle for your subscription product.
        Look for your subscription product in the list below and use its handle in the SubscriptionBanner component.
      </p>
      
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {products.map((product) => (
            <li key={product.id}>
              <div className="px-4 py-4 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {product.featuredImage && (
                      <div className="flex-shrink-0 h-12 w-12 mr-3">
                        <img 
                          src={product.featuredImage.url} 
                          alt={product.featuredImage.altText || product.title}
                          className="h-12 w-12 object-cover rounded-md"
                        />
                      </div>
                    )}
                    <div>
                      <p className="text-sm font-medium text-indigo-600 truncate">
                        {product.title}
                      </p>
                      <p className="mt-1 text-xs text-gray-500">
                        <span className="font-semibold">Handle:</span> {product.handle}
                      </p>
                    </div>
                  </div>
                  <div className="ml-2 flex-shrink-0 flex">
                    <Link
                      to={`/products/${product.handle}`}
                      className="px-2 py-1 text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                    >
                      View Product
                    </Link>
                  </div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

const ALL_PRODUCTS_QUERY = `#graphql
  query AllProducts {
    products(first: 100) {
      nodes {
        id
        title
        handle
        featuredImage {
          id
          url
          altText
        }
      }
    }
  }
` as const;
