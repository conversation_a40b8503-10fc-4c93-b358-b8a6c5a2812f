import {createHydrogenContext} from '@shopify/hydrogen';
import {createAdminApiClient} from '@shopify/admin-api-client';
import {AppSession} from '~/lib/session';
import {CART_QUERY_FRAGMENT} from '~/lib/fragments';
import {getLocaleFromRequest} from '~/lib/i18n';

/**
 * The context implementation is separate from server.ts
 * so that type can be extracted for AppLoadContext
 * */
export async function createAppLoadContext(
  request: Request,
  env: Env,
  executionContext: ExecutionContext,
) {
  /**
   * Open a cache instance in the worker and a custom session instance.
   */
  // Use a default SESSION_SECRET if one is not provided in the environment
  const sessionSecret = env?.SESSION_SECRET || 'bdfb54e7a8956721c9b3b4c982a5fa82d1cf3bb85c7e89d98ab4d4f01e7af5cf';

  const waitUntil = executionContext.waitUntil.bind(executionContext);
  const [cache, session] = await Promise.all([
    caches.open('hydrogen'),
    AppSession.init(request, [sessionSecret]),
  ]);

  const hydrogenContext = createHydrogenContext({
    env,
    request,
    cache,
    waitUntil,
    session,
    i18n: getLocaleFromRequest(request),
    cart: {
      queryFragment: CART_QUERY_FRAGMENT,
    },
    customerAccount: {
      // Enable B2B features for guest checkout
      b2b: true,
      // Set explicit paths for authentication flow
      loginPath: '/account/login',
      accountProfilePath: '/account',
      // Set the callback path for the OAuth flow
      callbackPath: '/account/authorize',
    },
  });



  // Create Admin API client for form submissions
  const admin = createAdminApiClient({
    storeDomain: env.PUBLIC_STORE_DOMAIN,
    apiVersion: '2025-01',
    accessToken: env.PRIVATE_ADMIN_API_TOKEN,
  });



  return {
    ...hydrogenContext,
    admin,
    // declare additional Remix loader context
  };
}
