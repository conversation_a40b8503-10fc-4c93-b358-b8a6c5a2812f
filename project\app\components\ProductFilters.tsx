import {useNavigate, useSearchParams} from '@remix-run/react';

// Updated coffee filters based on industry standards
const COFFEE_TYPES = [
  {label: 'All Products', value: 'all'},
  {label: 'Single Origin', value: 'single-origin'},
  {label: 'Signature Blends', value: 'signature-blends'},
  {label: 'Decaf', value: 'decaf'},
];

const ROAST_LEVELS = [
  {label: 'All Roasts', value: 'all'},
  {label: 'Light Roast', value: 'light'},
  {label: 'Medium Roast', value: 'medium'},
  {label: 'Dark Roast', value: 'dark'},
];

const ORIGINS = [
  {label: 'Ethiopia', value: 'ethiopia'},
  {label: 'Colombia', value: 'colombia'},
  {label: 'Brazil', value: 'brazil'},
  {label: 'Guatemala', value: 'guatemala'},
  {label: 'Costa Rica', value: 'costa-rica'},
  {label: 'Kenya', value: 'kenya'},
];

export function ProductFilters() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  // Get filter values from URL params
  const coffeeType = searchParams.get('type') || 'all';
  const roast = searchParams.get('roast') || 'all';
  const origin = searchParams.get('origin') || '';

  // Function to update filters in URL
  function updateFilter(key, value) {
    const newParams = new URLSearchParams(searchParams);
    
    if (value && value !== 'all') {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }
    
    navigate(`?${newParams.toString()}`);
  }

  return (
    <div className="product-filters">
      {/* Filter header */}
      <div className="flex items-center mb-6">
        <h2 className="text-lg font-medium text-gray-900">Filters</h2>
      </div>

      {/* Coffee Type */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Coffee Type</h3>
        <div className="space-y-1">
          {COFFEE_TYPES.map((type) => (
            <div 
              key={type.value} 
              className="flex items-center"
            >
              <input
                id={`type-${type.value}`}
                name="coffee-type"
                type="radio"
                checked={coffeeType === type.value}
                onChange={() => updateFilter('type', type.value)}
                className="h-4 w-4 text-primary-600 border-gray-300 focus:ring-primary-500"
              />
              <label 
                htmlFor={`type-${type.value}`}
                className="ml-3 text-sm text-gray-700 cursor-pointer"
              >
                {type.label}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Roast Level */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Roast Level</h3>
        <div className="space-y-1">
          {ROAST_LEVELS.map((level) => (
            <div 
              key={level.value} 
              className="flex items-center"
            >
              <input
                id={`roast-${level.value}`}
                name="roast-level"
                type="radio"
                checked={roast === level.value}
                onChange={() => updateFilter('roast', level.value)}
                className="h-4 w-4 text-primary-600 border-gray-300 focus:ring-primary-500"
              />
              <label 
                htmlFor={`roast-${level.value}`}
                className="ml-3 text-sm text-gray-700 cursor-pointer"
              >
                {level.label}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Origin */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Origin</h3>
        <div className="space-y-1">
          {ORIGINS.map((originOption) => (
            <div 
              key={originOption.value} 
              className="flex items-center"
            >
              <input
                id={`origin-${originOption.value}`}
                name="origin"
                type="radio"
                checked={origin === originOption.value}
                onChange={() => updateFilter('origin', originOption.value)}
                className="h-4 w-4 text-primary-600 border-gray-300 focus:ring-primary-500"
              />
              <label 
                htmlFor={`origin-${originOption.value}`}
                className="ml-3 text-sm text-gray-700 cursor-pointer"
              >
                {originOption.label}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Our Sourcing Journey */}
      <div className="mb-6 p-4 bg-gray-50 rounded-md border border-gray-200">
        <h3 className="text-sm font-medium text-gray-900 mb-2">Our Sourcing Journey</h3>
        <p className="text-xs text-gray-600 mb-3">
          Every bag of coffee represents a journey from high-altitude farms to your cup.
        </p>
        <button className="text-xs text-primary-600 font-medium flex items-center">
          Learn more
          <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      {/* Clear filters button */}
      <button
        onClick={() => {
          navigate('?');
        }}
        className="w-full py-2 px-4 bg-gray-100 border border-gray-300 rounded-md text-sm font-medium text-gray-600 hover:bg-gray-200 transition-colors"
      >
        Clear All Filters
      </button>
    </div>
  );
}