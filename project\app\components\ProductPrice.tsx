import {Money} from '@shopify/hydrogen';
import type {MoneyV2} from '@shopify/hydrogen/storefront-api-types';

export function ProductPrice({
  price,
  compareAtPrice,
  className = '',
}: {
  price?: MoneyV2;
  compareAtPrice?: MoneyV2 | null;
  className?: string;
}) {
  // Calculate discount percentage if there's a compare at price
  const getDiscountPercentage = () => {
    if (!compareAtPrice || !price) return null;

    const compareAtPriceValue = parseFloat(compareAtPrice.amount);
    const priceValue = parseFloat(price.amount);

    if (compareAtPriceValue <= priceValue) return null;

    const discountPercentage = Math.round(
      ((compareAtPriceValue - priceValue) / compareAtPriceValue) * 100
    );

    return discountPercentage;
  };

  const discountPercentage = getDiscountPercentage();

  return (
    <div className="product-price">
      {price ? (
        <span className={`text-2xl font-bold text-gray-900 ${className}`}>
          <Money data={price} />
        </span>
      ) : (
        <span className={`text-2xl font-bold text-gray-900 ${className}`}>&nbsp;</span>
      )}
    </div>
  );
}
