import { type MetaFunction } from '@remix-run/react';

export const meta: MetaFunction = () => {
  return [
    { title: 'Affiliate Program | The Good Coffee Company' },
    { description: 'Learn about our affiliate program and how you can earn commissions by promoting our premium coffee products.' }
  ];
};

export default function AffiliatePage() {
  // The affiliate link that was previously directly linked in the header
  const affiliateLink = "https://www.shoutout.global/signup?id=rckd8";

  return (
    <div className="bg-gray-50">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-[#1a2333] text-white">
        <div className="absolute inset-0 z-0 opacity-20">
          <img
            src="/coffee_cups.webp"
            alt="Coffee affiliate program background"
            className="w-full h-full object-cover object-center"
          />
        </div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 md:py-32">
          <div className="max-w-3xl">
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6">Affiliate Program</h1>
            <p className="text-base sm:text-lg md:text-xl text-gray-300 mb-6 sm:mb-8">
              Partner with us and earn commissions by promoting our premium coffee products.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-3xl mx-auto">
          {/* About the Affiliate Program */}
          <div className="mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">About Our Affiliate Program</h2>
            <p className="text-lg text-gray-700 mb-6">
              The Good Coffee Company has partnered with Shoutout to offer an affiliate program that allows you to earn commissions by promoting our premium coffee products on your website, blog, or social media channels.
            </p>
            <p className="text-lg text-gray-700 mb-6">
              Shoutout is a trusted affiliate marketing platform that makes it easy to track your referrals and earn commissions. When someone clicks on your unique affiliate link and makes a purchase, you'll earn a commission on the sale.
            </p>
          </div>

          {/* Benefits */}
          <div className="mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">Benefits of Our Affiliate Program</h2>
            <ul className="space-y-6">
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-[#1e3a8a] flex items-center justify-center shadow-sm">
                  <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-lg text-gray-700">Competitive commission rates on all sales</p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-[#1e3a8a] flex items-center justify-center shadow-sm">
                  <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-lg text-gray-700">Extended cookie duration to maximize your earnings</p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-[#1e3a8a] flex items-center justify-center shadow-sm">
                  <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-lg text-gray-700">Access to promotional materials and banners</p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-[#1e3a8a] flex items-center justify-center shadow-sm">
                  <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-lg text-gray-700">Detailed reporting and tracking of your performance</p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-[#1e3a8a] flex items-center justify-center shadow-sm">
                  <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-lg text-gray-700">Regular payments for your referrals</p>
                </div>
              </li>
            </ul>
          </div>

          {/* How It Works */}
          <div className="mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">How It Works</h2>
            <ol className="space-y-6">
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-[#1e3a8a] flex items-center justify-center text-white font-bold">
                  1
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Sign Up</h3>
                  <p className="text-lg text-gray-700">
                    Join Shoutout and apply to become an affiliate for The Good Coffee Company.
                  </p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-[#1e3a8a] flex items-center justify-center text-white font-bold">
                  2
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Get Your Links</h3>
                  <p className="text-lg text-gray-700">
                    Once approved, you'll receive your unique affiliate links and promotional materials.
                  </p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-[#1e3a8a] flex items-center justify-center text-white font-bold">
                  3
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Promote</h3>
                  <p className="text-lg text-gray-700">
                    Share your affiliate links on your website, blog, social media, or email newsletters.
                  </p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-[#1e3a8a] flex items-center justify-center text-white font-bold">
                  4
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Earn</h3>
                  <p className="text-lg text-gray-700">
                    Earn commissions on every sale made through your affiliate links.
                  </p>
                </div>
              </li>
            </ol>
          </div>

          {/* Join Now CTA */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 p-8 text-center">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">Ready to Join Our Affiliate Program?</h2>
            <p className="text-lg text-gray-700 mb-10 max-w-2xl mx-auto">
              Click the button below to sign up through Shoutout and start earning commissions by promoting our premium coffee products.
            </p>
            <a
              href={affiliateLink}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-10 py-4 rounded-lg font-medium bg-[#1e3a8a] text-white hover:bg-[#152a61] transition-all duration-300 shadow-md mt-4"
            >
              <span className="text-white">Join Our Affiliate Program</span>
              <svg className="w-5 h-5 ml-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
