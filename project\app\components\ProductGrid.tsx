import {Link} from '@remix-run/react';
import {Image, Money} from '@shopify/hydrogen';

export function ProductGrid({
  products,
  className,
}: {
  products: any[];
  className?: string;
}) {
  return (
    <div className={className}>
      {products.map((product) => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );
}

function ProductCard({product}: {product: any}) {
  const {price, compareAtPrice} = product.variants?.nodes[0] || {};
  const isDiscounted = compareAtPrice?.amount > (price?.amount || 0);

  return (
    <Link
      className="group relative"
      key={product.id}
      to={`/products/${product.handle}`}
    >
      <div className="aspect-h-1 aspect-w-1 w-full overflow-hidden rounded-lg bg-gray-100">
        {product.featuredImage && (
          <Image
            alt={product.featuredImage.altText || product.title}
            data={product.featuredImage}
            sizes="(min-width: 1024px) 33vw, (min-width: 768px) 50vw, 100vw"
            className="h-full w-full object-cover object-center transition-opacity group-hover:opacity-75"
          />
        )}
        {!product.availableForSale && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50">
            <span className="text-white font-medium px-4 py-2 bg-black/50 rounded-md">
              Sold out
            </span>
          </div>
        )}
      </div>
      <div className="mt-4 flex justify-between">
        <div>
          <h3 className="text-sm font-medium text-gray-900">{product.title}</h3>
          <p className="mt-1 text-sm text-gray-500">{product.vendor}</p>
        </div>
        <div className="text-right">
          <div className={`text-sm ${isDiscounted ? 'text-gray-500 line-through' : 'text-gray-900'}`}>
            <Money data={product.priceRange.minVariantPrice} />
          </div>
          {isDiscounted && price && (
            <div className="text-sm font-medium text-red-600">
              <Money data={price} />
            </div>
          )}
        </div>
      </div>
    </Link>
  );
}