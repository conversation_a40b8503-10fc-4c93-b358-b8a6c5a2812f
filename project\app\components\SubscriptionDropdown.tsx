import {useState} from 'react';
import {AddToCartButton} from './AddToCartButton';
import {useAside} from './Aside';
import type {ProductFragment, ProductVariantFragment} from 'storefrontapi.generated';

interface SubscriptionDropdownProps {
  product: ProductFragment;
  selectedVariant: ProductVariantFragment;
  quantity: number;
}

interface SubscriptionOption {
  id: string;
  name: string;
  description: string;
  sellingPlanId: string;
  savings?: string;
}

export function SubscriptionDropdown({
  product,
  selectedVariant,
  quantity
}: SubscriptionDropdownProps) {
  const { open } = useAside();
  const [selectedOption, setSelectedOption] = useState<string>('monthly');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Subscription options with selling plan IDs
  const subscriptionOptions: SubscriptionOption[] = [
    {
      id: 'weekly',
      name: 'Weekly',
      description: 'Delivered every week',
      sellingPlanId: 'gid://shopify/SellingPlan/4449239219',
    },
    {
      id: 'monthly',
      name: 'Monthly',
      description: 'Delivered every month',
      sellingPlanId: 'gid://shopify/SellingPlan/4447502515',
      savings: 'Most Popular'
    },
    {
      id: 'every3weeks',
      name: 'Every 3 Weeks',
      description: 'Delivered every 3 weeks',
      sellingPlanId: 'gid://shopify/SellingPlan/4449271987',
    },
    {
      id: 'every6weeks',
      name: 'Every 6 Weeks',
      description: 'Delivered every 6 weeks',
      sellingPlanId: 'gid://shopify/SellingPlan/4449304755',
    },
  ];

  const currentOption = subscriptionOptions.find(option => option.id === selectedOption) || subscriptionOptions[1];

  // Only show if variant is available
  if (!selectedVariant?.availableForSale) {
    return null;
  }

  return (
    <div className="mb-4 space-y-3">
      {/* Subscription Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Subscribe & Save
        </h3>
        <span className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">
          Free Shipping
        </span>
      </div>

      {/* Dropdown Selector */}
      <div className="relative">
        <button
          type="button"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className="w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-left shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400 transition-colors duration-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center">
                <span className="font-medium text-gray-900">{currentOption.name}</span>
                {currentOption.savings && (
                  <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full font-medium">
                    {currentOption.savings}
                  </span>
                )}
              </div>
              <span className="text-sm text-gray-500">{currentOption.description}</span>
            </div>
            <svg
              className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
                isDropdownOpen ? 'transform rotate-180' : ''
              }`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </button>

        {/* Dropdown Options */}
        {isDropdownOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
            {subscriptionOptions.map((option) => (
              <button
                key={option.id}
                type="button"
                onClick={() => {
                  setSelectedOption(option.id);
                  setIsDropdownOpen(false);
                }}
                className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 first:rounded-t-lg last:rounded-b-lg ${
                  selectedOption === option.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                }`}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center">
                      <span className={`font-medium ${
                        selectedOption === option.id ? 'text-blue-900' : 'text-gray-900'
                      }`}>
                        {option.name}
                      </span>
                      {option.savings && (
                        <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full font-medium">
                          {option.savings}
                        </span>
                      )}
                    </div>
                    <span className={`text-sm ${
                      selectedOption === option.id ? 'text-blue-700' : 'text-gray-500'
                    }`}>
                      {option.description}
                    </span>
                  </div>
                  {selectedOption === option.id && (
                    <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Subscribe Button */}
      <AddToCartButton
        onClick={() => {
          open('cart');
        }}
        lines={[
          {
            merchandiseId: selectedVariant.id,
            quantity: quantity,
            sellingPlanId: currentOption.sellingPlanId,
          },
        ]}
        className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-4 px-6 rounded-lg font-medium transition-all duration-200 flex items-center justify-center shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
      >
        <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        Subscribe {currentOption.name}
      </AddToCartButton>

      {/* Benefits */}
      <div className="text-xs text-gray-600 space-y-1">
        <div className="flex items-center">
          <svg className="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          Cancel or modify anytime
        </div>
        <div className="flex items-center">
          <svg className="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          Fresh, low acid, mold free coffee
        </div>
      </div>
    </div>
  );
}
