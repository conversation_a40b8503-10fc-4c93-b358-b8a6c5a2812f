import { useState, useEffect } from 'react';
import { EnhancedProductFilters } from './EnhancedProductFilters';

interface MobileFilterDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  activeFiltersCount?: number;
}

export function MobileFilterDrawer({
  isOpen,
  onClose,
  activeFiltersCount = 0,
}: MobileFilterDrawerProps) {
  const [isVisible, setIsVisible] = useState(false);

  // Handle animation states
  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
    } else {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 300); // Match this with the CSS transition duration
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // If not visible at all, don't render
  if (!isOpen && !isVisible) {
    return null;
  }

  return (
    <div
      className={`fixed inset-0 z-50 flex ${isVisible ? 'visible' : 'invisible'}`}
      aria-labelledby="filter-heading"
    >
      {/* Backdrop with improved styling */}
      <div
        className={`fixed inset-0 bg-black/60 backdrop-blur-sm transition-all duration-300 ${
          isOpen ? 'opacity-100' : 'opacity-0'
        }`}
        onClick={onClose}
      />

      {/* Drawer panel - modernized with better styling */}
      <div
        className={`relative ml-auto flex h-full w-full max-w-xs flex-col overflow-y-auto bg-white shadow-2xl transition-transform duration-300 ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        {/* Filters */}
        <div className="px-5 py-6 flex-1 overflow-y-auto">
          <EnhancedProductFilters
            showMobileHeader={true}
            onClose={onClose}
          />
        </div>

        {/* Apply button - enhanced with better styling */}
        <div className="border-t border-gray-100 px-5 py-4 mt-auto bg-gray-50">
          <button
            type="button"
            className="w-full bg-primary-600 border border-transparent rounded-lg py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-primary-700 focus:outline-none shadow-md transition-all duration-200 hover:shadow-lg"
            onClick={onClose}
          >
            <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
}
