import {json, type ActionFunctionArgs, type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {Form, useActionData, useLoaderData} from '@remix-run/react';

// Use the non-deprecated json function
const jsonResponse = (data: any, init?: ResponseInit) => {
  return new Response(JSON.stringify(data), {
    ...init,
    headers: {
      'Content-Type': 'application/json',
      ...init?.headers,
    },
  });
};

export async function loader({context}: LoaderFunctionArgs) {
  // Check if metaobject definitions already exist
  const admin = context.admin as any;
  
  try {
    const response = await admin.graphql(`
      query CheckMetaobjectDefinitions {
        metaobjectDefinitions(first: 10) {
          edges {
            node {
              id
              type
              name
            }
          }
        }
      }
    `);
    
    const data = await response.json();
    const definitions = data.data?.metaobjectDefinitions?.edges || [];
    
    const contactFormExists = definitions.some((def: any) => def.node.type === 'contact_form_submission');
    const wholesaleInquiryExists = definitions.some((def: any) => def.node.type === 'wholesale_inquiry');
    
    return jsonResponse({
      contactFormExists,
      wholesaleInquiryExists,
      definitions: definitions.map((def: any) => def.node)
    });
  } catch (error) {
    console.error('Error checking metaobject definitions:', error);
    return jsonResponse({
      contactFormExists: false,
      wholesaleInquiryExists: false,
      definitions: [],
      error: 'Failed to check existing definitions'
    });
  }
}

export async function action({request, context}: ActionFunctionArgs) {
  const admin = context.admin as any;
  const formData = await request.formData();
  const action = formData.get('action') as string;
  
  try {
    if (action === 'create-contact-form') {
      const response = await admin.graphql(`
        mutation CreateContactFormDefinition {
          metaobjectDefinitionCreate(definition: {
            type: "contact_form_submission"
            name: "Contact Form Submission"
            description: "Stores contact form submissions from the website"
            fieldDefinitions: [
              {
                key: "first_name"
                name: "First Name"
                description: "Customer's first name"
                type: {
                  name: "single_line_text_field"
                }
                required: true
              }
              {
                key: "last_name"
                name: "Last Name"
                description: "Customer's last name"
                type: {
                  name: "single_line_text_field"
                }
                required: true
              }
              {
                key: "email"
                name: "Email"
                description: "Customer's email address"
                type: {
                  name: "single_line_text_field"
                }
                required: true
              }
              {
                key: "phone"
                name: "Phone"
                description: "Customer's phone number"
                type: {
                  name: "single_line_text_field"
                }
                required: false
              }
              {
                key: "subject"
                name: "Subject"
                description: "Subject of the inquiry"
                type: {
                  name: "single_line_text_field"
                }
                required: true
              }
              {
                key: "message"
                name: "Message"
                description: "Customer's message"
                type: {
                  name: "multi_line_text_field"
                }
                required: true
              }
              {
                key: "submitted_at"
                name: "Submitted At"
                description: "When the form was submitted"
                type: {
                  name: "single_line_text_field"
                }
                required: true
              }
            ]
            access: {
              admin: MERCHANT_READ_WRITE
              storefront: NONE
            }
          }) {
            metaobjectDefinition {
              id
              type
              name
            }
            userErrors {
              field
              message
              code
            }
          }
        }
      `);
      
      const data = await response.json();
      
      if (data.errors || data.data?.metaobjectDefinitionCreate?.userErrors?.length > 0) {
        return jsonResponse({
          success: false,
          error: data.errors?.[0]?.message || data.data?.metaobjectDefinitionCreate?.userErrors?.[0]?.message || 'Failed to create contact form definition'
        });
      }
      
      return jsonResponse({
        success: true,
        message: 'Contact form metaobject definition created successfully!'
      });
    }
    
    if (action === 'create-wholesale-inquiry') {
      const response = await admin.graphql(`
        mutation CreateWholesaleInquiryDefinition {
          metaobjectDefinitionCreate(definition: {
            type: "wholesale_inquiry"
            name: "Wholesale Inquiry"
            description: "Stores wholesale program inquiries from the website"
            fieldDefinitions: [
              {
                key: "name"
                name: "Name"
                description: "Contact person's name"
                type: {
                  name: "single_line_text_field"
                }
                required: true
              }
              {
                key: "email"
                name: "Email"
                description: "Contact email address"
                type: {
                  name: "single_line_text_field"
                }
                required: true
              }
              {
                key: "phone"
                name: "Phone"
                description: "Contact phone number"
                type: {
                  name: "single_line_text_field"
                }
                required: false
              }
              {
                key: "company"
                name: "Company"
                description: "Company or organization name"
                type: {
                  name: "single_line_text_field"
                }
                required: true
              }
              {
                key: "business_type"
                name: "Business Type"
                description: "Type of business"
                type: {
                  name: "single_line_text_field"
                }
                required: true
              }
              {
                key: "message"
                name: "Message"
                description: "Additional details about their needs"
                type: {
                  name: "multi_line_text_field"
                }
                required: false
              }
              {
                key: "submitted_at"
                name: "Submitted At"
                description: "When the inquiry was submitted"
                type: {
                  name: "single_line_text_field"
                }
                required: true
              }
            ]
            access: {
              admin: MERCHANT_READ_WRITE
              storefront: NONE
            }
          }) {
            metaobjectDefinition {
              id
              type
              name
            }
            userErrors {
              field
              message
              code
            }
          }
        }
      `);
      
      const data = await response.json();
      
      if (data.errors || data.data?.metaobjectDefinitionCreate?.userErrors?.length > 0) {
        return jsonResponse({
          success: false,
          error: data.errors?.[0]?.message || data.data?.metaobjectDefinitionCreate?.userErrors?.[0]?.message || 'Failed to create wholesale inquiry definition'
        });
      }
      
      return jsonResponse({
        success: true,
        message: 'Wholesale inquiry metaobject definition created successfully!'
      });
    }
    
    return jsonResponse({
      success: false,
      error: 'Invalid action'
    });
    
  } catch (error) {
    console.error('Error creating metaobject definition:', error);
    return jsonResponse({
      success: false,
      error: 'An unexpected error occurred'
    });
  }
}

export default function SetupMetaobjects() {
  const loaderData = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  
  return (
    <div className="max-w-4xl mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Setup Metaobjects for Forms</h1>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Current Status</h2>
        <div className="space-y-2">
          <div className={`flex items-center ${loaderData.contactFormExists ? 'text-green-600' : 'text-red-600'}`}>
            <span className="mr-2">{loaderData.contactFormExists ? '✅' : '❌'}</span>
            Contact Form Submission metaobject {loaderData.contactFormExists ? 'exists' : 'missing'}
          </div>
          <div className={`flex items-center ${loaderData.wholesaleInquiryExists ? 'text-green-600' : 'text-red-600'}`}>
            <span className="mr-2">{loaderData.wholesaleInquiryExists ? '✅' : '❌'}</span>
            Wholesale Inquiry metaobject {loaderData.wholesaleInquiryExists ? 'exists' : 'missing'}
          </div>
        </div>
      </div>
      
      {actionData?.success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <p className="text-green-800">{actionData.message}</p>
        </div>
      )}
      
      {actionData?.error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-800">{actionData.error}</p>
        </div>
      )}
      
      <div className="space-y-6">
        {!loaderData.contactFormExists && (
          <div className="border rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Create Contact Form Metaobject</h3>
            <p className="text-gray-600 mb-4">
              This will create the metaobject definition needed for the contact form to work.
            </p>
            <Form method="post">
              <input type="hidden" name="action" value="create-contact-form" />
              <button
                type="submit"
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
              >
                Create Contact Form Metaobject
              </button>
            </Form>
          </div>
        )}
        
        {!loaderData.wholesaleInquiryExists && (
          <div className="border rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Create Wholesale Inquiry Metaobject</h3>
            <p className="text-gray-600 mb-4">
              This will create the metaobject definition needed for the wholesale form to work.
            </p>
            <Form method="post">
              <input type="hidden" name="action" value="create-wholesale-inquiry" />
              <button
                type="submit"
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
              >
                Create Wholesale Inquiry Metaobject
              </button>
            </Form>
          </div>
        )}
        
        {loaderData.contactFormExists && loaderData.wholesaleInquiryExists && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-2">✅ All Set!</h3>
            <p className="text-green-800">
              Both metaobject definitions exist. Your contact and wholesale forms should now work properly.
            </p>
          </div>
        )}
      </div>
      
      {loaderData.definitions.length > 0 && (
        <div className="mt-8">
          <h3 className="text-lg font-semibold mb-4">Existing Metaobject Definitions</h3>
          <div className="bg-gray-50 rounded-lg p-4">
            <ul className="space-y-2">
              {loaderData.definitions.map((def: any) => (
                <li key={def.id} className="flex justify-between">
                  <span>{def.name}</span>
                  <span className="text-gray-500">{def.type}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}
