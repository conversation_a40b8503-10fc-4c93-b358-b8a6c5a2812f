# Production Setup Guide - The Good Coffee Company

## 🚀 Pre-Deployment Checklist

### ✅ Code Changes Completed
- [x] **Google Analytics ID updated** from 
- [x] **GoogleAnalytics component enabled** in root.tsx
- [x] **Customer Account API routes** properly configured
- [x] **All authentication flows** implemented and tested locally

---

## 🔧 Required Shopify Admin Configuration

### 1. Customer Account API Setup (CRITICAL)

**⚠️ This MUST be done before deployment or customer accounts won't work**

1. **Open Shopify Admin**
   - Go to **Settings** → **Apps and sales channels**
   - Click **Hydrogen** (or **Headless** if using that)

2. **Navigate to Customer Account API Settings**
   - Click your storefront name
   - Click **Storefront settings**
   - Click **Customer Account API**

3. **Configure Production Domain**
   - Under **Application setup**, click **Edit** ✎
   - **Callback URI(s)**: Add your production domain + `/account/authorize`
     ```
     https://yourdomain.com/account/authorize
     ```
   - **JavaScript origin(s)**: Add your production domain
     ```
     https://yourdomain.com
     ```
   - **Logout URI**: Add your production domain
     ```
     https://yourdomain.com
     ```

4. **Client Type Settings**
   - Ensure **Client type** is set to **Public**
   - This enables JavaScript origins and Logout URI options

5. **Save Configuration**
   - Click **Save** to apply changes

### 2. Environment Variables (Auto-configured on Oxygen)

When deploying to Oxygen, these are automatically set:
- `PUBLIC_CUSTOMER_ACCOUNT_API_CLIENT_ID`
- `PUBLIC_CUSTOMER_ACCOUNT_API_URL`
- `PUBLIC_CHECKOUT_DOMAIN`
- `PUBLIC_STORE_DOMAIN`
- `PUBLIC_STOREFRONT_ID`
- `PUBLIC_STOREFRONT_API_TOKEN`

---

## 📊 Google Analytics 4 Verification

### Production Testing Steps

1. **Deploy your site** to production
2. **Open your live website**
3. **Open browser Developer Tools** (F12)
4. **Go to Console tab**
5. **Test GA4 is loaded**:
   ```javascript
   console.log(typeof window.gtag); // Should return "function"
   console.log(window.dataLayer); // Should show array with events
   ```

6. **Navigate through your site** and perform actions:
   - View products
   - Add items to cart
   - Search for products
   - View collections

7. **Check Google Analytics Real-Time**:
   - Go to [Google Analytics](https://analytics.google.com)
   - Select your property 
   - Go to **Reports** → **Real-time**
   - You should see live activity

### Expected GA4 Events
- `page_view` - Page navigation
- `view_item` - Product page views
- `view_item_list` - Collection page views
- `add_to_cart` - Adding products to cart
- `remove_from_cart` - Removing products from cart
- `view_cart` - Opening cart sidebar
- `begin_checkout` - Starting checkout process
- `search` - Search queries

---

## 🔐 Customer Account Testing

### After Domain Configuration

1. **Test Login Flow**:
   - Go to `/account/login`
   - Click "Sign In"
   - Should redirect to Shopify login
   - After login, should redirect back to your site

2. **Test Account Pages**:
   - `/account` - Account dashboard
   - `/account/orders` - Order history
   - `/account/profile` - Profile management
   - `/account/addresses` - Address management
   - `/account/subscriptions` - Subscription management

3. **Test Logout**:
   - Click logout button
   - Should clear session and redirect to homepage

### Common Issues & Solutions

**"redirect_uri mismatch" Error**:
- ✅ Verify Callback URI in Shopify Admin matches exactly
- ✅ Ensure domain includes `https://` and `/account/authorize`
- ✅ Check for trailing slashes or typos

**Customer Account API not working**:
- ✅ Confirm Client type is set to "Public"
- ✅ Verify all three URIs are configured (Callback, JavaScript origin, Logout)
- ✅ Wait 5-10 minutes after saving changes for propagation

---

## 🚀 Deployment Commands

### Deploy to Oxygen

```bash
# Build and deploy to production
npx shopify hydrogen deploy

# Or deploy to preview first
npx shopify hydrogen deploy --preview
```

### Verify Deployment

1. **Check build success**
2. **Test customer accounts immediately**
3. **Verify Google Analytics in real-time**
4. **Test all major user flows**

---

## 📋 Post-Deployment Verification

### Immediate Tests (First 30 minutes)

- [ ] Homepage loads correctly
- [ ] Video intro works (desktop) / skips (mobile)
- [ ] Product pages display properly
- [ ] Add to cart functionality works
- [ ] Cart sidebar opens and functions
- [ ] Customer login/logout works
- [ ] Google Analytics events fire
- [ ] Subscription products work
- [ ] Search functionality works

### Extended Tests (First 24 hours)

- [ ] Monitor Google Analytics for data
- [ ] Check Shopify Analytics dashboard
- [ ] Test customer account features thoroughly
- [ ] Verify subscription management works
- [ ] Test on multiple devices and browsers
- [ ] Monitor for any console errors

---

## 🆘 Troubleshooting

### Customer Accounts Not Working
1. Double-check Shopify Admin Customer Account API settings
2. Verify domain matches exactly (including https://)
3. Clear browser cache and cookies
4. Test in incognito/private browsing mode

### Google Analytics Not Tracking
1. Verify GA4 property ID is correct (G-M14J3Y9HZ3)
2. Check browser console for gtag errors
3. Ensure privacy banner is accepted
4. Test in different browsers

### General Issues
1. Check browser console for JavaScript errors
2. Verify all environment variables are set
3. Test in different browsers and devices
4. Monitor Shopify Admin for any alerts

---

## ✅ Success Criteria

Your production deployment is successful when:

- ✅ Customer accounts work (login/logout/account pages)
- ✅ Google Analytics shows real-time data
- ✅ All major site functions work
- ✅ No console errors on key pages
- ✅ Subscription functionality works
- ✅ Cart and checkout flow complete

---

## 📞 Support Resources

- **Shopify Hydrogen Docs**: https://shopify.dev/docs/storefronts/headless/hydrogen
- **Customer Account API**: https://shopify.dev/docs/storefronts/headless/building-with-the-customer-account-api
- **Google Analytics 4**: https://support.google.com/analytics/answer/9304153
