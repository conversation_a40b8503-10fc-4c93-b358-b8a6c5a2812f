import {type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {useLoaderData, Link, type MetaFunction} from '@remix-run/react';

export const meta: MetaFunction = () => {
  return [{title: 'Policies | The Good Coffee Company'}];
};

export async function loader({context}: LoaderFunctionArgs) {
  const data = await context.storefront.query(POLICIES_QUERY);
  const policies = Object.values(data.shop || {});

  if (!policies.length) {
    throw new Response('No policies found', {status: 404});
  }

  return {policies};
}

export default function Policies() {
  const {policies} = useLoaderData<typeof loader>();

  // Define policy icons
  const policyIcons = {
    'privacy-policy': (
      <svg width="32" height="32" className="text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
      </svg>
    ),
    'shipping-policy': (
      <svg width="32" height="32" className="text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
      </svg>
    ),
    'terms-of-service': (
      <svg width="32" height="32" className="text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
    ),
    'refund-policy': (
      <svg width="32" height="32" className="text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2-2v16l4-2 4 2 4-2 4 2z" />
      </svg>
    ),
    'subscription-policy': (
      <svg width="32" height="32" className="text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
      </svg>
    ),
  };

  // Get policy descriptions
  const getPolicyDescription = (handle) => {
    switch (handle) {
      case 'privacy-policy':
        return 'Learn how we collect, use, and protect your personal information.';
      case 'shipping-policy':
        return 'Information about our shipping methods, timeframes, and costs.';
      case 'terms-of-service':
        return 'The rules and guidelines for using our website and services.';
      case 'refund-policy':
        return 'Our policies regarding returns, exchanges, and refunds.';
      case 'subscription-policy':
        return 'Details about our subscription services and recurring billing.';
      default:
        return 'Important information about our policies and procedures.';
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Header */}
      <div className="text-center mb-12">
        <span className="text-white font-semibold text-sm uppercase tracking-wider bg-[#1a2333] px-4 py-1 rounded-full shadow-sm inline-block mb-4">Our Policies</span>
        <h1 className="text-4xl font-bold text-gray-900 mb-6">Our Policies</h1>
        <div className="w-24 h-1 bg-[#1a2333] rounded mb-8 mx-auto"></div>
        <div className="flex justify-center">
          <div className="text-center max-w-3xl">
            <p className="text-xl text-gray-800 font-medium leading-relaxed">
              We're committed to transparency and fairness in all our business practices.
              Please review our policies to understand how we operate and what you can expect from us.
            </p>
          </div>
        </div>
      </div>

      {/* Policy cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {policies.map((policy) => {
          if (!policy) return null;

          const icon = policyIcons[policy.handle] || (
            <svg width="32" height="32" className="text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          );

          const description = getPolicyDescription(policy.handle);

          return (
            <Link
              key={policy.id}
              to={`/policies/${policy.handle}`}
              className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
            >
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="bg-primary-50 rounded-lg p-3 mr-4">
                    {icon}
                  </div>
                  <h2 className="text-xl font-semibold text-gray-900">{policy.title}</h2>
                </div>
                <p className="text-gray-600 mb-4">{description}</p>
                <div className="flex items-center text-primary-600 font-medium">
                  Read more
                  <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Additional information */}
      <div className="mt-16 bg-gray-50 rounded-xl p-8">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Have Questions About Our Policies?</h2>
          <p className="text-gray-600 mb-6">
            If you have any questions or concerns about our policies, please don't hesitate to contact our customer service team.
            We're here to help and provide clarification on any aspect of our business practices.
          </p>
          <Link
            to="/pages/contact"
            className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary-600 hover:bg-primary-700"
          >
            Contact Us
            <svg className="ml-2 -mr-1 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </Link>
        </div>
      </div>
    </div>
  );
}

const POLICIES_QUERY = `#graphql
  fragment PolicyItem on ShopPolicy {
    id
    title
    handle
  }
  query Policies ($country: CountryCode, $language: LanguageCode)
    @inContext(country: $country, language: $language) {
    shop {
      privacyPolicy {
        ...PolicyItem
      }
      shippingPolicy {
        ...PolicyItem
      }
      termsOfService {
        ...PolicyItem
      }
      refundPolicy {
        ...PolicyItem
      }
      subscriptionPolicy {
        id
        title
        handle
      }
    }
  }
` as const;
