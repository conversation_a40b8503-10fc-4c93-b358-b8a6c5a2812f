import { Link } from '@remix-run/react';
import { Image } from '@shopify/hydrogen';

interface CollectionHeaderProps {
  title: string;
  description?: string;
  image?: any;
  productCount: number;
  handle: string;
}

export function CollectionHeader({
  title,
  description,
  image,
  productCount,
  handle,
}: CollectionHeaderProps) {
  return (
    <div className="mb-8">
      {/* Breadcrumbs */}
      <nav className="flex mb-4 text-sm">
        <ol className="flex items-center space-x-1">
          <li>
            <Link to="/" className="text-gray-500 hover:text-gray-700">
              Home
            </Link>
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </li>
          <li>
            <Link to="/collections" className="text-gray-500 hover:text-gray-700">
              Collections
            </Link>
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </li>
          <li>
            <span className="text-gray-900 font-medium">{title}</span>
          </li>
        </ol>
      </nav>

      {/* Hero section with image */}
      {image && (
        <div className="relative rounded-xl overflow-hidden mb-6 aspect-[21/9]">
          <Image
            alt={image.altText || title}
            data={image}
            sizes="100vw"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent" />
          <div className="absolute inset-0 flex flex-col items-center justify-center text-center p-6">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              {title}
            </h1>
            {description && (
              <p className="text-white/90 max-w-2xl mb-6 hidden md:block">
                {description}
              </p>
            )}
          </div>
        </div>
      )}

      {/* Title section (when no image) */}
      {!image && (
        <div className="mb-6">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">{title}</h1>
          {description && (
            <p className="text-gray-600 max-w-2xl">{description}</p>
          )}
        </div>
      )}

      {/* Collection stats */}
      <div className="flex flex-wrap items-center justify-between gap-4 py-3 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">
            {productCount} {productCount === 1 ? 'product' : 'products'}
          </span>
          <span className="h-1 w-1 bg-gray-300 rounded-full"></span>
          <span className="text-sm text-gray-600">
            {handle === 'all' ? 'All coffee' : title}
          </span>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">
            Free shipping on orders over $40
          </span>
          <span className="h-4 w-px bg-gray-200"></span>
          <span className="text-xs text-gray-500">
            Ethically sourced
          </span>
        </div>
      </div>
    </div>
  );
}
