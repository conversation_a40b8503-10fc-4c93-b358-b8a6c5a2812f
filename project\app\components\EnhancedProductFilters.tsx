import {useNavigate, useSearchParams} from '@remix-run/react';
import {useState} from 'react';

// Updated coffee filters based on industry standards
const COFFEE_TYPES = [
  {label: 'All Products', value: 'all'},
  {label: 'Single Origin', value: 'single-origin'},
  {label: 'Signature Blends', value: 'signature-blends'},
  {label: 'Decaf', value: 'decaf'},
];

const ROAST_LEVELS = [
  {label: 'All Roasts', value: 'all'},
  {label: 'Light Roast', value: 'light'},
  {label: 'Medium Roast', value: 'medium'},
  {label: 'Dark Roast', value: 'dark'},
];

const ORIGINS = [
  {label: 'Ethiopia', value: 'ethiopia'},
  {label: 'Colombia', value: 'colombia'},
  {label: 'Brazil', value: 'brazil'},
  {label: 'Guatemala', value: 'guatemala'},
  {label: 'Costa Rica', value: 'costa-rica'},
  {label: 'Kenya', value: 'kenya'},
];

const PRICE_RANGES = [
  {label: 'All Prices', value: 'all'},
  {label: 'Under $15', min: '0', max: '15'},
  {label: '$15 - $25', min: '15', max: '25'},
  {label: '$25 - $50', min: '25', max: '50'},
  {label: 'Over $50', min: '50', max: '1000'},
];

interface EnhancedProductFiltersProps {
  className?: string;
  showMobileHeader?: boolean;
  onClose?: () => void;
}

export function EnhancedProductFilters({
  className = '',
  showMobileHeader = false,
  onClose,
}: EnhancedProductFiltersProps) {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [expandedSections, setExpandedSections] = useState({
    type: true,
    roast: true,
    origin: true,
    price: true,
  });

  // Get filter values from URL params
  const coffeeType = searchParams.get('type') || 'all';
  const roast = searchParams.get('roast') || 'all';
  const origin = searchParams.get('origin') || '';
  const minPrice = searchParams.get('minPrice') || '';
  const maxPrice = searchParams.get('maxPrice') || '';

  // Count active filters
  const activeFiltersCount = [
    coffeeType !== 'all' ? 1 : 0,
    roast !== 'all' ? 1 : 0,
    origin ? 1 : 0,
    minPrice || maxPrice ? 1 : 0,
  ].reduce((a, b) => a + b, 0);

  // Function to update filters in URL
  function updateFilter(key, value) {
    const newParams = new URLSearchParams(searchParams);

    if (value && value !== 'all') {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }

    navigate(`?${newParams.toString()}`);
  }

  // Function to update price range
  function updatePriceRange(min, max) {
    const newParams = new URLSearchParams(searchParams);

    if (min && max && min !== '0' && max !== '1000') {
      newParams.set('minPrice', min);
      newParams.set('maxPrice', max);
    } else if (min === '0' && max !== '1000') {
      newParams.delete('minPrice');
      newParams.set('maxPrice', max);
    } else if (min !== '0' && max === '1000') {
      newParams.set('minPrice', min);
      newParams.delete('maxPrice');
    } else {
      newParams.delete('minPrice');
      newParams.delete('maxPrice');
    }

    navigate(`?${newParams.toString()}`);
  }

  // Function to toggle section expansion
  function toggleSection(section) {
    setExpandedSections({
      ...expandedSections,
      [section]: !expandedSections[section],
    });
  }

  // Function to clear all filters
  function clearAllFilters() {
    navigate('?');
  }

  // Function to check if a price range is selected
  function isPriceRangeSelected(min, max) {
    if (min === '0' && maxPrice === max) return true;
    if (max === '1000' && minPrice === min) return true;
    if (minPrice === min && maxPrice === max) return true;
    return false;
  }

  return (
    <div className={`product-filters ${className}`}>
      {/* Mobile header with improved styling */}
      {showMobileHeader && (
        <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            Filters
            {activeFiltersCount > 0 && (
              <span className="ml-2 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 shadow-sm">
                {activeFiltersCount}
              </span>
            )}
          </h2>
          {onClose && (
            <button
              type="button"
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors rounded-full hover:bg-gray-100"
              onClick={onClose}
            >
              <span className="sr-only">Close panel</span>
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      )}

      {/* Filter header for desktop with improved styling */}
      {!showMobileHeader && (
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            Refine Selection
            {activeFiltersCount > 0 && (
              <span className="ml-2 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 shadow-sm">
                {activeFiltersCount}
              </span>
            )}
          </h2>
        </div>
      )}

      {/* Active filters - redesigned with better visual hierarchy */}
      {activeFiltersCount > 0 && (
        <div className="mb-8 bg-gray-50 p-4 rounded-xl border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-gray-900">Selected Filters</h3>
            <button
              onClick={clearAllFilters}
              className="text-xs text-primary-600 hover:text-primary-800 font-medium flex items-center"
            >
              <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Reset All
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            {coffeeType !== 'all' && (
              <button
                onClick={() => updateFilter('type', 'all')}
                className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-white text-gray-800 border border-gray-200 shadow-sm hover:bg-gray-50 transition-colors"
              >
                {COFFEE_TYPES.find(t => t.value === coffeeType)?.label}
                <svg className="w-3.5 h-3.5 ml-1.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
            {roast !== 'all' && (
              <button
                onClick={() => updateFilter('roast', 'all')}
                className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-white text-gray-800 border border-gray-200 shadow-sm hover:bg-gray-50 transition-colors"
              >
                <span className={`w-2 h-2 rounded-full mr-1.5 ${
                  roast === 'dark' ? 'bg-[#3a2213]' :
                  roast === 'medium' ? 'bg-[#8c5e2a]' :
                  'bg-[#c8a27a]'
                }`}></span>
                {ROAST_LEVELS.find(r => r.value === roast)?.label}
                <svg className="w-3.5 h-3.5 ml-1.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
            {origin && (
              <button
                onClick={() => updateFilter('origin', '')}
                className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-white text-gray-800 border border-gray-200 shadow-sm hover:bg-gray-50 transition-colors"
              >
                <svg className="w-3 h-3 mr-1.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {ORIGINS.find(o => o.value === origin)?.label}
                <svg className="w-3.5 h-3.5 ml-1.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
            {(minPrice || maxPrice) && (
              <button
                onClick={() => {
                  updatePriceRange('', '');
                }}
                className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-white text-gray-800 border border-gray-200 shadow-sm hover:bg-gray-50 transition-colors"
              >
                <svg className="w-3 h-3 mr-1.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {minPrice && maxPrice ? `$${minPrice} - $${maxPrice}` : minPrice ? `Over $${minPrice}` : `Under $${maxPrice}`}
                <svg className="w-3.5 h-3.5 ml-1.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        </div>
      )}

      {/* Coffee Type - modernized with better styling */}
      <div className="mb-8">
        <button
          className="flex w-full items-center justify-between text-sm font-semibold text-gray-900 mb-4 bg-white p-3 rounded-lg shadow-sm hover:shadow transition-shadow"
          onClick={() => toggleSection('type')}
        >
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-2 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
            </svg>
            Coffee Type
          </span>
          <svg
            className={`w-5 h-5 transition-transform duration-300 text-gray-500 ${expandedSections.type ? 'transform rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        {expandedSections.type && (
          <div className="space-y-2 pl-2">
            {COFFEE_TYPES.map((type) => (
              <div
                key={type.value}
                className="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
              >
                <input
                  id={`type-${type.value}`}
                  name="coffee-type"
                  type="radio"
                  checked={coffeeType === type.value}
                  onChange={() => updateFilter('type', type.value)}
                  className="h-4 w-4 text-primary-600 border-gray-300 focus:ring-primary-500"
                />
                <label
                  htmlFor={`type-${type.value}`}
                  className="ml-3 text-sm text-gray-700 cursor-pointer font-medium"
                >
                  {type.label}
                </label>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Roast Level - modernized with color indicators */}
      <div className="mb-8">
        <button
          className="flex w-full items-center justify-between text-sm font-semibold text-gray-900 mb-4 bg-white p-3 rounded-lg shadow-sm hover:shadow transition-shadow"
          onClick={() => toggleSection('roast')}
        >
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-2 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            Roast Level
          </span>
          <svg
            className={`w-5 h-5 transition-transform duration-300 text-gray-500 ${expandedSections.roast ? 'transform rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        {expandedSections.roast && (
          <div className="space-y-2 pl-2">
            {ROAST_LEVELS.map((level) => (
              <div
                key={level.value}
                className="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
              >
                <input
                  id={`roast-${level.value}`}
                  name="roast-level"
                  type="radio"
                  checked={roast === level.value}
                  onChange={() => updateFilter('roast', level.value)}
                  className="h-4 w-4 text-primary-600 border-gray-300 focus:ring-primary-500"
                />
                <label
                  htmlFor={`roast-${level.value}`}
                  className="ml-3 text-sm text-gray-700 cursor-pointer font-medium flex items-center"
                >
                  {level.value !== 'all' && (
                    <span className={`w-3 h-3 rounded-full mr-2 ${
                      level.value === 'dark' ? 'bg-[#3a2213]' :
                      level.value === 'medium' ? 'bg-[#8c5e2a]' :
                      'bg-[#c8a27a]'
                    }`}></span>
                  )}
                  {level.label}
                </label>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Origin - modernized with map icon */}
      <div className="mb-8">
        <button
          className="flex w-full items-center justify-between text-sm font-semibold text-gray-900 mb-4 bg-white p-3 rounded-lg shadow-sm hover:shadow transition-shadow"
          onClick={() => toggleSection('origin')}
        >
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-2 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Origin
          </span>
          <svg
            className={`w-5 h-5 transition-transform duration-300 text-gray-500 ${expandedSections.origin ? 'transform rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        {expandedSections.origin && (
          <div className="grid grid-cols-2 gap-2 pl-2">
            {ORIGINS.map((originOption) => (
              <div
                key={originOption.value}
                className="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
              >
                <input
                  id={`origin-${originOption.value}`}
                  name="origin"
                  type="radio"
                  checked={origin === originOption.value}
                  onChange={() => updateFilter('origin', originOption.value)}
                  className="h-4 w-4 text-primary-600 border-gray-300 focus:ring-primary-500"
                />
                <label
                  htmlFor={`origin-${originOption.value}`}
                  className="ml-3 text-sm text-gray-700 cursor-pointer font-medium"
                >
                  {originOption.label}
                </label>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Price Range - modernized with price slider */}
      <div className="mb-8">
        <button
          className="flex w-full items-center justify-between text-sm font-semibold text-gray-900 mb-4 bg-white p-3 rounded-lg shadow-sm hover:shadow transition-shadow"
          onClick={() => toggleSection('price')}
        >
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-2 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Price Range
          </span>
          <svg
            className={`w-5 h-5 transition-transform duration-300 text-gray-500 ${expandedSections.price ? 'transform rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        {expandedSections.price && (
          <div className="space-y-2 pl-2">
            {PRICE_RANGES.map((range) => (
              <div
                key={range.label}
                className="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
              >
                <input
                  id={`price-${range.label}`}
                  name="price-range"
                  type="radio"
                  checked={range.value === 'all' ? !minPrice && !maxPrice : isPriceRangeSelected(range.min, range.max)}
                  onChange={() => updatePriceRange(range.min, range.max)}
                  className="h-4 w-4 text-primary-600 border-gray-300 focus:ring-primary-500"
                />
                <label
                  htmlFor={`price-${range.label}`}
                  className="ml-3 text-sm text-gray-700 cursor-pointer font-medium"
                >
                  {range.label}
                </label>
              </div>
            ))}
          </div>
        )}
      </div>



      {/* Clear filters button - enhanced with better styling */}
      <button
        onClick={clearAllFilters}
        className="w-full py-3 px-4 bg-white border border-gray-200 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors shadow-sm hover:shadow flex items-center justify-center"
      >
        <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        Reset All Filters
      </button>
    </div>
  );
}
