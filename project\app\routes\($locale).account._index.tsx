import {redirect, type LoaderFunctionArgs} from '@shopify/remix-oxygen';

export async function loader({context, request}: LoaderFunctionArgs) {
  try {
    await context.customerAccount.handleAuthStatus();
    return redirect('/account/orders');
  } catch (error) {
    // If authentication fails, redirect to login
    const url = new URL(request.url);
    return redirect(`/account/login?returnTo=${encodeURIComponent(url.pathname)}`);
  }
}
