import {useEffect, useState} from 'react';
import {type MetaFunction} from '@remix-run/react';
import {redirect, type LoaderFunctionArgs} from '@shopify/remix-oxygen';

export const meta: MetaFunction = () => {
  return [{title: 'My Subscriptions'}];
};

export async function loader({context, request}: LoaderFunctionArgs) {
  try {
    await context.customerAccount.handleAuthStatus();
    return {};
  } catch (error) {
    // If authentication fails, redirect to login
    const url = new URL(request.url);
    return redirect(`/account/login?returnTo=${encodeURIComponent(url.pathname)}`);
  }
}

export default function Subscriptions() {
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [showManualLink, setShowManualLink] = useState(false);

  // The URL to your Seal Subscriptions login page
  // This is the actual Seal Subscriptions portal URL
  const subscriptionLoginUrl = `https://01jwc3mrg1n50bp1nmx0wwk7h0-6eda004f65bbee5cdcbe.myshopify.com/a/subscriptions/login`;

  useEffect(() => {
    // Show manual link after a short delay to give user control
    const timer = setTimeout(() => {
      setShowManualLink(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleRedirect = () => {
    setIsRedirecting(true);
    window.location.href = subscriptionLoginUrl;
  };

  return (
    <div className="subscriptions">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-1">My Subscriptions</h2>
        <p className="text-sm text-gray-500">
          Manage your coffee subscriptions
        </p>
      </div>

      <div className="bg-blue-50 p-6 rounded-lg border border-blue-200 mb-8">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-medium text-blue-800">Access Your Subscription Management</h3>
            <div className="mt-2 text-blue-700">
              <p>Click the button below to access the subscription login form. You'll enter your email address and receive a secure link to manage all your coffee subscriptions.</p>
            </div>
            <div className="mt-4 space-y-3">
              <button
                onClick={handleRedirect}
                disabled={isRedirecting}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-[#1a2333] hover:bg-[#2a3343] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1a2333] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isRedirecting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Redirecting...
                  </>
                ) : (
                  <>
                    <svg className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Get Subscription Access Link
                  </>
                )}
              </button>
              {showManualLink && (
                <p className="text-sm text-blue-600">
                  <a
                    href={subscriptionLoginUrl}
                    className="underline hover:text-blue-800"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Or click here if the button doesn't work
                  </a>
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">How to Access Your Subscriptions</h3>
        <div className="space-y-3 text-gray-600">
          <div className="flex items-start">
            <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">1</span>
            <p>Click the "Get Subscription Access Link" button above</p>
          </div>
          <div className="flex items-start">
            <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">2</span>
            <p>Enter your email address (the one used for your subscription order)</p>
          </div>
          <div className="flex items-start">
            <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">3</span>
            <p>Check your email for a secure magic link to access your subscription portal</p>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 mb-4">What You Can Do in the Portal</h3>
        <p className="text-gray-600 mb-4">
          In the Seal Subscriptions customer portal, you can:
        </p>
        <ul className="space-y-2 text-gray-600 mb-6">
          <li className="flex items-start">
            <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>View all your active subscriptions</span>
          </li>
          <li className="flex items-start">
            <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Change delivery frequency (weekly, monthly, etc.)</span>
          </li>
          <li className="flex items-start">
            <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Skip upcoming deliveries when you need a break</span>
          </li>
          <li className="flex items-start">
            <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Update your payment methods</span>
          </li>
          <li className="flex items-start">
            <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Change your shipping address</span>
          </li>
          <li className="flex items-start">
            <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Cancel your subscription if needed</span>
          </li>
        </ul>
        <p className="text-gray-600 italic mb-6">
          Note: You can also access your subscription management portal through the links in your subscription-related emails.
        </p>

        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
          <h4 className="text-sm font-medium text-yellow-800 mb-2">Alternative Access Methods</h4>
          <div className="text-sm text-yellow-700 space-y-2">
            <p>If the subscription portal link isn't working, you can access your subscriptions through:</p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>The magic links in your subscription confirmation emails</li>
              <li>The links in your recurring order notification emails</li>
              <li>Contacting our support team for direct assistance</li>
            </ul>
            <p className="mt-3">
              <strong>Need help?</strong> Contact us at{' '}
              <a href="mailto:<EMAIL>" className="underline hover:text-yellow-900">
                <EMAIL>
              </a>{' '}
              or call{' '}
              <a href="tel:+14124378239" className="underline hover:text-yellow-900">
                (*************
              </a>.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
