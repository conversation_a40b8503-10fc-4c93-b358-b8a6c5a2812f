import {Link, useLoaderData, type MetaFunction} from '@remix-run/react';
import {
  Money,
  getPaginationVariables,
  flattenConnection,
} from '@shopify/hydrogen';
import {redirect, type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {CUSTOMER_ORDERS_QUERY} from '~/graphql/customer-account/CustomerOrdersQuery';
import type {
  CustomerOrdersFragment,
  OrderItemFragment,
} from 'customer-accountapi.generated';
import {PaginatedResourceSection} from '~/components/PaginatedResourceSection';

export const meta: MetaFunction = () => {
  return [{title: 'Orders'}];
};

export async function loader({request, context}: LoaderFunctionArgs) {
  try {
    // First check authentication status
    await context.customerAccount.handleAuthStatus();

    const paginationVariables = getPaginationVariables(request, {
      pageBy: 20,
    });

    const {data, errors} = await context.customerAccount.query(
      CUSTOMER_ORDERS_QUERY,
      {
        variables: {
          ...paginationVariables,
        },
      },
    );

    if (errors?.length || !data?.customer) {
      // Instead of throwing an error, redirect to login
      const url = new URL(request.url);
      return redirect(`/account/login?returnTo=${encodeURIComponent(url.pathname)}`);
    }

    return {customer: data.customer};
  } catch (error) {
    // If any authentication error occurs, redirect to login
    const url = new URL(request.url);
    return redirect(`/account/login?returnTo=${encodeURIComponent(url.pathname)}`);
  }
}

export default function Orders() {
  const {customer} = useLoaderData<{customer: CustomerOrdersFragment}>();
  const {orders} = customer;
  return (
    <div className="orders">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-1">My Orders</h2>
        <p className="text-sm text-gray-500">
          View and track your order history
        </p>
      </div>

      {orders.nodes.length ? <OrdersTable orders={orders} /> : <EmptyOrders />}
    </div>
  );
}

function OrdersTable({orders}: Pick<CustomerOrdersFragment, 'orders'>) {
  return (
    <div className="account-orders">
      {orders?.nodes.length ? (
        <div className="overflow-hidden border border-gray-200 rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <PaginatedResourceSection connection={orders}>
                {({node: order}) => <OrderItem key={order.id} order={order} />}
              </PaginatedResourceSection>
            </tbody>
          </table>
        </div>
      ) : (
        <EmptyOrders />
      )}
    </div>
  );
}

function EmptyOrders() {
  return (
    <div className="text-center py-12 bg-gray-50 rounded-lg border border-gray-200">
      <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
      </svg>
      <h3 className="mt-2 text-sm font-medium text-gray-900">No orders found</h3>
      <p className="mt-1 text-sm text-gray-500">You haven't placed any orders yet.</p>
      <div className="mt-6">
        <Link
          to="/buy"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm !text-white bg-[#1a2333] hover:bg-[#2a3749] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1a2333]"
        >
          Start Shopping
          <svg className="ml-2 -mr-1 h-4 w-4 !text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </Link>
      </div>
    </div>
  );
}

function OrderItem({order}: {order: OrderItemFragment}) {
  const fulfillmentStatus = flattenConnection(order.fulfillments)[0]?.status;
  const orderDate = new Date(order.processedAt);
  const formattedDate = orderDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  // Map status to badge colors
  const getStatusBadgeClass = (status: string) => {
    switch(status?.toLowerCase()) {
      case 'fulfilled':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'partially_fulfilled':
        return 'bg-yellow-100 text-yellow-800';
      case 'unfulfilled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusBadgeClass = (status: string) => {
    switch(status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'refunded':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <tr>
      <td className="px-6 py-4 whitespace-nowrap">
        <Link
          to={`/account/orders/${btoa(order.id)}`}
          className="text-[#1a2333] hover:text-[#2a3749] font-medium"
        >
          #{order.number}
        </Link>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {formattedDate}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex flex-col space-y-1">
          {fulfillmentStatus && (
            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(fulfillmentStatus)}`}>
              {fulfillmentStatus.replace(/_/g, ' ')}
            </span>
          )}
          {order.financialStatus && (
            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPaymentStatusBadgeClass(order.financialStatus)}`}>
              {order.financialStatus.replace(/_/g, ' ')}
            </span>
          )}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <Money data={order.totalPrice} />
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <Link
          to={`/account/orders/${btoa(order.id)}`}
          className="text-[#1a2333] hover:text-[#2a3749]"
        >
          View Details
        </Link>
      </td>
    </tr>
  );
}
