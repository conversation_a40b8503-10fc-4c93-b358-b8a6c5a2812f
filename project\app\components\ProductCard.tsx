import {Link} from '@remix-run/react';
import {Image, Money} from '@shopify/hydrogen';
import {AddToCartButton} from '~/components/AddToCartButton';

export function ProductCard({
  product,
  loading,
}: {
  product: any;
  loading?: 'eager' | 'lazy';
}) {
  const firstVariantImage = product.featuredImage;

  // Function to determine badge type - in a real app, this would be based on product data
  const getBadgeType = () => {
    if (product.title.toLowerCase().includes('dark')) {
      return 'Dark Roast';
    } else if (product.title.toLowerCase().includes('premium')) {
      return 'Premium';
    } else if (product.title.toLowerCase().includes('single')) {
      return 'Single Origin';
    } else {
      return 'Premium';
    }
  };

  // Function to get badge color class - in a real app, based on product data or tag
  const getBadgeColorClass = (badgeType: string) => {
    switch (badgeType) {
      case 'Dark Roast':
        return 'bg-primary-700 text-white';
      case 'Premium':
        return 'bg-primary-600 text-white';
      case 'Single Origin':
        return 'bg-primary-600 text-white';
      default:
        return 'bg-primary-600 text-white';
    }
  };

  const badgeType = getBadgeType();
  const badgeColorClass = getBadgeColorClass(badgeType);

  // Get display price
  const price = product.priceRange.minVariantPrice.amount;
  const formattedPrice = parseFloat(price).toFixed(2);

  // Get first available variant for add to cart
  const firstVariant = product.variants?.nodes?.[0];

  return (
    <div className="product-card group bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
      {/* Product badge */}
      <div className="relative">
        <div className={`absolute top-3 left-3 z-10 px-3 py-1 text-xs font-medium rounded-full ${badgeColorClass}`}>
          {badgeType}
        </div>

        {/* New badge - could be conditionally shown based on product data */}
        <div className="absolute top-3 right-3 z-10 px-3 py-1 text-xs font-medium rounded-full bg-yellow-500 text-white">
          New
        </div>

        {/* Product image */}
        <Link to={`/products/${product.handle}`} prefetch="intent" className="block overflow-hidden">
          <div className="aspect-square bg-gray-50 relative overflow-hidden">
            {firstVariantImage ? (
              <Image
                alt={firstVariantImage.altText || product.title}
                aspectRatio="1/1"
                data={firstVariantImage}
                loading={loading}
                sizes="(min-width: 1024px) 25vw, (min-width: 768px) 33vw, 50vw"
                className="w-full h-full object-contain object-center mix-blend-multiply transition-transform duration-700 group-hover:scale-110"
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-500">
                <svg style={{width: '3rem', height: '3rem'}} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            )}

            {/* Quick add overlay */}
            <div className="absolute inset-0 bg-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
              <Link
                to={`/products/${product.handle}`}
                className="bg-white/90 backdrop-blur-sm text-gray-900 px-4 py-2 rounded-lg font-medium transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300 hover:bg-white"
              >
                View Product
              </Link>
            </div>
          </div>
        </Link>
      </div>

      {/* Product info */}
      <div className="p-5">
        <div className="flex items-center mb-2">
          {/* Star rating - could be dynamic based on product reviews */}
          <div className="flex text-yellow-400 mr-2">
            <svg className="w-4 h-4 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
            <svg className="w-4 h-4 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
            <svg className="w-4 h-4 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
            <svg className="w-4 h-4 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
            <svg className="w-4 h-4 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
          </div>
          <span className="text-xs text-gray-500">(24 reviews)</span>
        </div>

        <h3 className="text-base font-semibold text-gray-900 mb-1 group-hover:text-primary-600 transition-colors">
          <Link to={`/products/${product.handle}`} prefetch="intent">
            {product.title}
          </Link>
        </h3>

        {/* Origin badge */}
        <div className="mt-1 mb-3">
          <span className="inline-flex items-center text-xs text-gray-500">
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Origin: Various Regions
          </span>
        </div>

        <div className="flex justify-between items-center pt-2 border-t border-gray-100">
          <div className="text-lg font-bold text-gray-900">
            ${formattedPrice}
          </div>

          <Link
            to={`/products/${product.handle}`}
            className="hidden md:flex text-xs font-medium text-gray-500 hover:text-gray-700 items-center"
          >
            Details
            <svg style={{width: 'var(--icon-size-sm)', height: 'var(--icon-size-sm)'}} className="ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </div>

        {/* Add to Cart Button */}
        <div className="mt-3">
          {firstVariant && product.availableForSale ? (
            <AddToCartButton
              disabled={!firstVariant.availableForSale}
              lines={[
                {
                  merchandiseId: firstVariant.id,
                  quantity: 1,
                },
              ]}
              variant="primary"
              className="w-full bg-[#1a2333] text-white py-2.5 px-4 rounded-lg hover:bg-[#2a3343] transition-colors duration-300 flex items-center justify-center text-sm font-medium"
            >
              <svg width="16" height="16" className="mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z" />
              </svg>
              Add to Cart
            </AddToCartButton>
          ) : (
            <button
              disabled
              className="w-full bg-gray-300 text-gray-500 py-2.5 px-4 rounded-lg cursor-not-allowed text-sm font-medium"
            >
              Sold Out
            </button>
          )}
        </div>
      </div>
    </div>
  );
}