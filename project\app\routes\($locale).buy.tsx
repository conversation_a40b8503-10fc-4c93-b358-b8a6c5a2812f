import {type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {useLoaderData, type MetaFunction, useSearchParams, useNavigate} from '@remix-run/react';
import {getPaginationVariables} from '@shopify/hydrogen';
import {EnhancedProductGrid} from '~/components/EnhancedProductGrid';
import {EnhancedProductFilters} from '~/components/EnhancedProductFilters';
import {MobileFilterDrawer} from '~/components/MobileFilterDrawer';
import {SubscriptionBanner} from '~/components/SubscriptionBanner';
import {useState} from 'react';

export const meta: MetaFunction<typeof loader> = () => {
  return [{title: `The Good Coffee Company | Buy Premium Coffee`}];
};

export async function loader(args: LoaderFunctionArgs) {
  const {request, context} = args;
  const {storefront} = context;
  const searchParams = new URL(request.url).searchParams;

  const paginationVariables = getPaginationVariables(request, {
    pageBy: 24,
  });

  // Build query string for filtering
  let queryString = '';

  // Get all filter values
  const coffeeType = searchParams.get('type');
  const roast = searchParams.get('roast');
  const origin = searchParams.get('origin');

  // Log the filter values for debugging
  console.log('Filter values:', { coffeeType, roast, origin });

  // Create a more general query that will match products with any of the selected tags
  // This is a more permissive approach that will show more products
  let tagFilters = [];

  if (coffeeType && coffeeType !== 'all') {
    // Try different tag formats that might be used in your store
    tagFilters.push(`tag:${coffeeType}`);
    tagFilters.push(`tag:*${coffeeType}*`);
    tagFilters.push(`title:*${coffeeType}*`);
  }

  if (roast && roast !== 'all') {
    // Try different formats for roast
    tagFilters.push(`tag:${roast}`);
    tagFilters.push(`tag:*${roast}*`);
    tagFilters.push(`tag:${roast}-roast`);
    tagFilters.push(`tag:*${roast}-roast*`);
    tagFilters.push(`title:*${roast}*`);
  }

  if (origin) {
    // Try different formats for origin
    tagFilters.push(`tag:${origin}`);
    tagFilters.push(`tag:*${origin}*`);
    tagFilters.push(`tag:origin-${origin}`);
    tagFilters.push(`tag:*origin-${origin}*`);
    tagFilters.push(`title:*${origin}*`);
  }

  // Combine filters with OR operator if we have any
  if (tagFilters.length > 0) {
    queryString = tagFilters.join(' OR ');
    console.log('Generated query string:', queryString);
  }

  // Add price filter
  const minPrice = searchParams.get('minPrice');
  const maxPrice = searchParams.get('maxPrice');

  // Create a separate price filter string
  let priceFilterString = '';
  if (minPrice || maxPrice) {
    if (minPrice) priceFilterString += `variants.price:>=${minPrice} `;
    if (maxPrice) priceFilterString += `variants.price:<=${maxPrice} `;
  }

  // Combine tag filters and price filters
  if (priceFilterString) {
    if (queryString) {
      // If we have both tag and price filters, wrap the tag filters in parentheses
      // and combine with AND operator for the price filter
      queryString = `(${queryString}) AND ${priceFilterString}`;
    } else {
      // If we only have price filters
      queryString = priceFilterString;
    }
  }

  // Log the final query string
  console.log('Final query string:', queryString);

  // Get sort parameter
  const sortKey = searchParams.get('sort');
  let sortVariable = 'BEST_SELLING'; // Default sort

  if (sortKey) {
    switch (sortKey) {
      case 'price-asc':
        sortVariable = 'PRICE';
        break;
      case 'price-desc':
        sortVariable = 'PRICE';
        // We'll handle the reverse in the query
        break;
      case 'title-asc':
        sortVariable = 'TITLE';
        break;
      case 'title-desc':
        sortVariable = 'TITLE';
        // We'll handle the reverse in the query
        break;
      case 'newest':
        sortVariable = 'CREATED';
        break;
      default:
        sortVariable = 'BEST_SELLING';
    }
  }

  // Determine if we need to reverse the sort order
  const reverse = sortKey === 'price-desc' || sortKey === 'title-desc';

  // First try with the filters
  const [{products: filteredProducts}] = await Promise.all([
    storefront.query(CATALOG_QUERY, {
      variables: {
        ...paginationVariables,
        query: queryString.trim() || null,
        sortKey: sortVariable,
        reverse,
      },
    }),
  ]);

  // If no products found with filters and filters are applied, fetch all products
  let products = filteredProducts;
  const hasActiveFilters = coffeeType !== 'all' || roast !== 'all' || origin || minPrice || maxPrice;

  if (hasActiveFilters && (!filteredProducts.nodes || filteredProducts.nodes.length === 0)) {
    console.log('No products found with filters. Showing all products as fallback.');

    // Get all products without filters as a fallback
    const [{products: allProducts}] = await Promise.all([
      storefront.query(CATALOG_QUERY, {
        variables: {
          ...paginationVariables,
          query: null, // No filters
          sortKey: sortVariable,
          reverse,
        },
      }),
    ]);

    // Use all products but mark that filters didn't work
    products = allProducts;
  }

  // Check if we're showing all products as a fallback
  const showingAllProductsFallback = hasActiveFilters &&
    (!filteredProducts.nodes || filteredProducts.nodes.length === 0) &&
    products.nodes && products.nodes.length > 0;

  return {
    products,
    appliedFilters: {
      coffeeType: coffeeType || 'all',
      roast: roast || 'all',
      origin: origin || '',
      minPrice,
      maxPrice,
      sort: sortKey || 'best-selling',
    },
    showingAllProductsFallback,
  };
}

export default function BuyCoffee() {
  const {products, appliedFilters, showingAllProductsFallback} = useLoaderData<typeof loader>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);

  // Count active filters
  const activeFiltersCount = [
    appliedFilters.coffeeType !== 'all' ? 1 : 0,
    appliedFilters.roast !== 'all' ? 1 : 0,
    appliedFilters.origin ? 1 : 0,
    appliedFilters.minPrice || appliedFilters.maxPrice ? 1 : 0,
  ].reduce((a, b) => a + b, 0);

  // Function to clear all filters
  const clearAllFilters = () => {
    navigate('/buy');
  };

  return (
    <div>
      {/* Subscription Banner */}
      <div className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SubscriptionBanner />
        </div>
      </div>

      {/* Product Listing Section - modernized with better layout and styling */}
      <div id="product-listing" className="bg-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center w-full mb-12">
            <h1 className="text-4xl md:text-5xl font-extrabold text-gray-900 mb-4">The Good Coffee</h1>
            <div className="w-24 h-1 bg-[#1e3a8a] rounded mb-6 mx-auto"></div>
            <p className="text-gray-800 text-lg font-medium">Discover our carefully curated collection of exceptional coffees from around the world.</p>
          </div>

          <div className="flex justify-end mb-6">
            {/* Mobile filter button - enhanced with better styling */}
            <div className="lg:hidden w-full md:w-auto">
              <button
                type="button"
                className="flex items-center justify-center w-full py-3 px-5 border border-gray-200 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none transition-all duration-200 hover:shadow"
                onClick={() => setIsFilterDrawerOpen(true)}
              >
                <svg className="w-5 h-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 010 2H4a1 1 0 01-1-1zm0 8a1 1 0 011-1h16a1 1 0 010 2H4a1 1 0 01-1-1zm0 8a1 1 0 011-1h16a1 1 0 010 2H4a1 1 0 01-1-1z" />
                </svg>
                Filter Products
                {activeFiltersCount > 0 && (
                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 shadow-sm">
                    {activeFiltersCount}
                  </span>
                )}
              </button>
            </div>
          </div>

          <div className="flex flex-col lg:flex-row gap-8">
            {/* Desktop Filters - with sticky positioning */}
            <div className="hidden lg:block lg:w-72">
              <div className="sticky top-24">
                <EnhancedProductFilters />
              </div>
            </div>

            {/* Product grid - enhanced with better styling */}
            <div className="flex-1">
              {!products?.nodes?.length ? (
                <div className="flex flex-col items-center justify-center py-16 px-4 bg-white rounded-xl shadow-sm border border-gray-100">
                  <div className="bg-gray-50 w-20 h-20 rounded-full flex items-center justify-center mb-6">
                    <svg className="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">No products found</h2>
                  <p className="text-gray-600 max-w-md text-center mb-6">We couldn't find any products matching your current filters. Try adjusting your selection or browse our categories below.</p>
                  <button
                    onClick={clearAllFilters}
                    className="inline-flex items-center px-5 py-2.5 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none shadow-sm transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Reset Filters
                  </button>
                </div>
              ) : (
                <>
                  {/* Show notification when filters don't match but we're showing all products */}
                  {showingAllProductsFallback && (
                    <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg shadow-sm">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-yellow-800">Filter Notice</h3>
                          <div className="mt-1 text-sm text-yellow-700">
                            <p>No products matched your selected filters. Showing all products instead.</p>
                          </div>
                          <div className="mt-2">
                            <button
                              onClick={clearAllFilters}
                              className="inline-flex items-center px-3 py-1.5 border border-yellow-300 rounded-md text-xs font-medium text-yellow-800 bg-yellow-50 hover:bg-yellow-100 focus:outline-none transition-colors"
                            >
                              Clear Filters
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                <EnhancedProductGrid
                  products={products.nodes}
                  title=""
                  enableFiltering={false}
                  enableSorting={true}
                  columnsDesktop={3}
                  columnsMobile={2}
                  highlightNewest={true}
                />
                </>

              )}
            </div>
          </div>

          {/* Mobile filter drawer */}
          <MobileFilterDrawer
            isOpen={isFilterDrawerOpen}
            onClose={() => setIsFilterDrawerOpen(false)}
            activeFiltersCount={activeFiltersCount}
          />
        </div>
      </div>



      {/* Roast Guide */}
      <div className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-8 text-center">Find Your Perfect Roast</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Light Roast */}
            <div className="text-center p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
              <div className="w-20 h-20 md:w-24 md:h-24 rounded-full bg-[#c8a27a] mx-auto mb-4 flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">Light</span>
              </div>
              <h3 className="text-xl font-bold mb-2">Light Roast</h3>
              <p className="text-gray-600 mb-4">Bright, acidic, with floral and fruity notes. Preserves the bean's original character.</p>
              <a
                href="?roast=light"
                className="inline-block px-4 py-2 bg-primary-50 text-primary-700 rounded-lg hover:bg-primary-100 transition-colors"
              >
                Shop Light Roast
              </a>
            </div>

            {/* Medium Roast */}
            <div className="text-center p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
              <div className="w-20 h-20 md:w-24 md:h-24 rounded-full bg-[#8c5e2a] mx-auto mb-4 flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">Medium</span>
              </div>
              <h3 className="text-xl font-bold mb-2">Medium Roast</h3>
              <p className="text-gray-600 mb-4">Balanced flavor with caramel sweetness. The perfect middle ground for most coffee lovers.</p>
              <a
                href="?roast=medium"
                className="inline-block px-4 py-2 bg-primary-50 text-primary-700 rounded-lg hover:bg-primary-100 transition-colors"
              >
                Shop Medium Roast
              </a>
            </div>

            {/* Dark Roast */}
            <div className="text-center p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
              <div className="w-20 h-20 md:w-24 md:h-24 rounded-full bg-[#3a2213] mx-auto mb-4 flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">Dark</span>
              </div>
              <h3 className="text-xl font-bold mb-2">Dark Roast</h3>
              <p className="text-gray-600 mb-4">Bold, rich flavor with chocolate and nutty notes. Less acidity with a fuller body.</p>
              <a
                href="?roast=dark"
                className="inline-block px-4 py-2 bg-primary-50 text-primary-700 rounded-lg hover:bg-primary-100 transition-colors"
              >
                Shop Dark Roast
              </a>
            </div>
          </div>
        </div>
      </div>



      {/* Brewing Guide Teaser */}
      <div className="bg-gray-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-[#1a2333] rounded-xl overflow-hidden shadow-xl">
            <div className="md:flex">
              <div className="md:w-1/2 p-10 md:p-14 flex flex-col justify-center">
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">Brewing Guides</h2>
                <div className="w-20 h-1 bg-white/30 rounded mb-8"></div>
                <p className="text-gray-300 mb-12 text-lg leading-relaxed">
                  Learn how to brew the perfect cup with our detailed guides for various brewing methods.
                  From pour-over to French press, we've got you covered.
                </p>
                <div className="mt-6">
                  <a href="/pages/brewing-guides" className="bg-[#d1d5db] text-gray-700 px-8 py-4 rounded-lg hover:bg-[#9ca3af] transition-all duration-300 inline-block font-medium">
                    View Brewing Guides
                  </a>
                </div>
              </div>
              <div className="md:w-1/2">
                <img
                  src="/brewing_3.webp"
                  alt="Coffee brewing"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

const CATALOG_QUERY = `#graphql
  fragment MoneyFragment on MoneyV2 {
    amount
    currencyCode
  }

  query Catalog(
    $country: CountryCode
    $language: LanguageCode
    $first: Int
    $last: Int
    $startCursor: String
    $endCursor: String
    $query: String
    $sortKey: ProductSortKeys
    $reverse: Boolean
  ) @inContext(country: $country, language: $language) {
    products(
      first: $first,
      last: $last,
      before: $startCursor,
      after: $endCursor,
      sortKey: $sortKey,
      reverse: $reverse,
      query: $query
    ) {
      nodes {
        id
        title
        handle
        vendor
        availableForSale
        tags
        createdAt

        featuredImage {
          id
          altText
          url
          width
          height
        }

        priceRange {
          minVariantPrice {
            ...MoneyFragment
          }
          maxVariantPrice {
            ...MoneyFragment
          }
        }

        variants(first: 1) {
          nodes {
            id
            availableForSale
            selectedOptions {
              name
              value
            }
            price {
              ...MoneyFragment
            }
            compareAtPrice {
              ...MoneyFragment
            }
          }
        }
      }
      pageInfo {
        hasPreviousPage
        hasNextPage
        startCursor
        endCursor
      }
    }
  }
` as const;
