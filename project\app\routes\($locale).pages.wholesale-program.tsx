import {Form, useActionData, useNavigation, Link, type MetaFunction} from '@remix-run/react';
import {type ActionFunctionArgs} from '@shopify/remix-oxygen';
import {useState, useEffect} from 'react';

// Type definitions for better type safety
interface WholesaleFormData {
  name: string;
  email: string;
  phone?: string;
  company: string;
  businessType: string;
  message?: string;
}

interface ActionData {
  success?: boolean;
  errors?: Record<string, string>;
  error?: string;
}

// Use the non-deprecated json function
const json = (data: any, init?: ResponseInit) => {
  return new Response(JSON.stringify(data), {
    ...init,
    headers: {
      'Content-Type': 'application/json',
      ...init?.headers,
    },
  });
};

export const meta: MetaFunction = () => {
  return [{title: 'Wholesale Program | The Good Coffee Company'}];
};

export async function action({request, context}: ActionFunctionArgs): Promise<Response> {
  const admin = context.admin as any;

  // Parse form data
  const formData = await request.formData();
  const name = formData.get('name') as string;
  const email = formData.get('email') as string;
  const phone = formData.get('phone') as string || '';
  const company = formData.get('company') as string;
  const businessType = formData.get('businessType') as string;
  const message = formData.get('message') as string || '';

  // Enhanced validation with email format check
  const errors: Record<string, string> = {};
  if (!name?.trim()) errors.name = 'Name is required';
  if (!email?.trim()) {
    errors.email = 'Email is required';
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.email = 'Please enter a valid email address';
  }
  if (!company?.trim()) errors.company = 'Company name is required';
  if (!businessType?.trim()) errors.businessType = 'Business type is required';

  if (Object.keys(errors).length) {
    return json({success: false, errors}, {status: 400});
  }

  try {
    // Create metaobject entry using Admin API
    const data = await admin.request(`
      mutation CreateWholesaleInquiry($input: MetaobjectCreateInput!) {
        metaobjectCreate(metaobject: $input) {
          metaobject {
            id
            handle
          }
          userErrors {
            field
            message
            code
          }
        }
      }
    `, {
      variables: {
        input: {
          type: "wholesale_inquiry",
          fields: [
            { key: "name", value: name.trim() },
            { key: "email", value: email.trim().toLowerCase() },
            { key: "phone", value: phone.trim() },
            { key: "company", value: company.trim() },
            { key: "business_type", value: businessType.trim() },
            { key: "message", value: message.trim() },
            { key: "submitted_at", value: new Date().toISOString() }
          ]
        }
      }
    });

    // Check for GraphQL errors
    if (data.errors) {
      console.error('GraphQL errors:', data.errors);
      return json({
        success: false,
        error: 'Failed to submit inquiry. Please try again.'
      }, { status: 500 });
    }

    // Check for user errors from the mutation
    if (data.data?.metaobjectCreate?.userErrors?.length > 0) {
      const userErrors = data.data.metaobjectCreate.userErrors;
      console.error('User errors:', userErrors);

      // Map user errors to form field errors if possible
      const fieldErrors: Record<string, string> = {};
      userErrors.forEach((error: any) => {
        if (error.field) {
          fieldErrors[error.field] = error.message;
        }
      });

      return json({
        success: false,
        errors: Object.keys(fieldErrors).length > 0 ? fieldErrors : undefined,
        error: Object.keys(fieldErrors).length === 0 ? 'Failed to submit inquiry. Please check your information and try again.' : undefined
      }, { status: 400 });
    }

    // Success case
    if (data.data?.metaobjectCreate?.metaobject?.id) {
      console.log('Wholesale inquiry submitted successfully:', data.data.metaobjectCreate.metaobject.id);
      return json({ success: true });
    }

    // Fallback error case
    return json({
      success: false,
      error: 'Unexpected response from server. Please try again.'
    }, { status: 500 });

  } catch (error) {
    console.error('Error creating wholesale inquiry:', error);
    return json({
      success: false,
      error: 'Unable to submit inquiry at this time. Please try again later or contact us directly.'
    }, { status: 500 });
  }
}

export default function WholesaleProgramPage() {
  const actionData = useActionData<ActionData>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === 'submitting';
  const [formSubmitted, setFormSubmitted] = useState(false);

  // If form was submitted successfully through the action, update the state
  useEffect(() => {
    if (actionData?.success && !formSubmitted) {
      setFormSubmitted(true);
    }
  }, [actionData?.success, formSubmitted]);

  return (
    <div className="bg-[#f8f4ef]">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-[#1a2333] text-white">
        <div className="absolute inset-0 z-0 opacity-30">
          <img
            src="/blurry_guy_with_bag.webp"
            alt="Person with coffee bag"
            className="w-full h-full object-cover object-center"
          />
        </div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Wholesale Program</h1>
            <p className="text-xl text-gray-300 mb-12">
              Start your coffee brand in a day! Partner with us for premium coffee products,
              competitive pricing, and exceptional service.
            </p>
            <div className="mt-4">
              <a
                href="#inquiry-form"
                className="inline-flex items-center px-6 py-3 bg-[#d1d5db] text-black rounded-lg font-medium hover:bg-[#9ca3af] transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                <span className="text-black">Wholesale Inquiry</span>
                <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="black">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* What We Offer Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold mb-6">What We Offer</h2>
          <div className="w-24 h-1 bg-[#1e3a8a] rounded mx-auto mb-8"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="p-8">
              <div className="bg-[#e6eeff] rounded-full w-16 h-16 flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-[#1e3a8a]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-4">Roasted Coffee for Multiple Origins</h3>
              <p className="text-gray-600">
                Explore our diverse selection of roasted coffee sourced from the finest coffee-growing regions around the world.
                We offer a variety of origins to suit your customers' preferences, ensuring a consistent and high-quality cup every time.
              </p>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="p-8">
              <div className="bg-[#e6eeff] rounded-full w-16 h-16 flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-[#1e3a8a]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-4">Green Coffee Beans</h3>
              <p className="text-gray-600">
                For those who prefer to roast their own, we provide top-tier green coffee beans.
                Our green coffee selection is sourced directly from farmers who share our commitment to quality and sustainability.
              </p>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="p-8">
              <div className="bg-[#e6eeff] rounded-full w-16 h-16 flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-[#1e3a8a]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-4">Custom White Labeling</h3>
              <p className="text-gray-600">
                Elevate your brand with our custom white labeling services. Whether you're launching a new coffee brand or
                expanding your current product line, we offer fully customizable packaging solutions that reflect your brand's unique identity.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Who We Serve Section */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-6">Who We Serve</h2>
            <div className="w-24 h-1 bg-[#1e3a8a] rounded mx-auto mb-8"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-[#f0f4ff] rounded-xl p-8 shadow-sm border border-gray-100">
              <h3 className="text-xl font-semibold mb-4">Coffee Shops</h3>
              <p className="text-gray-600">
                Enhance your café's offerings with our premium coffee products. We supply both roasted and green coffee
                tailored to meet the demands of your customers, ensuring they keep coming back for more.
              </p>
            </div>

            <div className="bg-[#f0f4ff] rounded-xl p-8 shadow-sm border border-gray-100">
              <h3 className="text-xl font-semibold mb-4">Nonprofits</h3>
              <p className="text-gray-600">
                We understand the unique needs of nonprofit organizations. Our customizable coffee solutions can help you
                raise funds while promoting a cause, all with the backing of high-quality coffee.
              </p>
            </div>

            <div className="bg-[#f0f4ff] rounded-xl p-8 shadow-sm border border-gray-100">
              <h3 className="text-xl font-semibold mb-4">Retail Stores</h3>
              <p className="text-gray-600">
                Stock your shelves with our top-quality coffee products. Whether you're looking for roasted coffee,
                green beans, or custom-labeled packages, we've got you covered.
              </p>
            </div>

            <div className="bg-[#f0f4ff] rounded-xl p-8 shadow-sm border border-gray-100">
              <h3 className="text-xl font-semibold mb-4">Online Coffee Businesses</h3>
              <p className="text-gray-600">
                Boost your online coffee sales with our wholesale products and white labeling services. We help you create
                a strong brand presence that resonates with your customers and stands out in the crowded online marketplace.
              </p>
            </div>

            <div className="bg-[#f0f4ff] rounded-xl p-8 shadow-sm border border-gray-100">
              <h3 className="text-xl font-semibold mb-4">Customized Fundraisers</h3>
              <p className="text-gray-600">
                Looking for a unique way to raise funds? Our customized fundraiser solutions offer a delicious and profitable option.
                We'll work with you to create a tailored coffee product that aligns with your fundraising goals.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold mb-6">Why Partner With Us</h2>
          <div className="w-24 h-1 bg-[#1e3a8a] rounded mx-auto mb-8"></div>
          <div className="flex justify-center">
            <div className="text-center max-w-4xl">
              <p className="text-lg text-gray-600">
                Joining our Wholesale Program offers you exclusive access to competitive pricing and high-quality products.
                Here's what sets our wholesale program apart:
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-12 w-12 rounded-md bg-[#1e3a8a] text-white">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">Competitive Pricing</h3>
              <p className="mt-2 text-gray-600">
                Benefit from wholesale pricing that helps maximize your profit margins while still offering premium products.
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-12 w-12 rounded-md bg-[#1e3a8a] text-white">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">Flexible Ordering</h3>
              <p className="mt-2 text-gray-600">
                No minimum order requirements, allowing you to order exactly what you need when you need it.
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-12 w-12 rounded-md bg-[#1e3a8a] text-white">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">Dedicated Support</h3>
              <p className="mt-2 text-gray-600">
                Get personalized account management and expert advice to help your business grow.
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-12 w-12 rounded-md bg-[#1e3a8a] text-white">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">Marketing Resources</h3>
              <p className="mt-2 text-gray-600">
                Access to marketing materials and resources to help promote your coffee products effectively.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Inquiry Form Section */}
      <div id="inquiry-form" className="bg-white py-16">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-6">Wholesale Inquiry</h2>
            <div className="w-24 h-1 bg-[#1e3a8a] rounded mx-auto mb-8"></div>
            <p className="text-lg text-gray-600">
              Interested in our wholesale program? Fill out the form below and our team will get back to you within 24 hours.
            </p>
          </div>

          {formSubmitted ? (
            <div className="bg-green-50 border border-green-200 rounded-xl p-8 text-center">
              <svg className="w-16 h-16 text-green-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Thank You!</h3>
              <p className="text-lg text-gray-600 mb-6">
                Your wholesale inquiry has been submitted successfully. Our team will contact you shortly.
              </p>
              <Link
                to="/"
                className="inline-flex items-center px-6 py-3 bg-[#d1d5db] text-black rounded-lg font-medium hover:bg-[#9ca3af] transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                Return to Homepage
                <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              </Link>
            </div>
          ) : (
            <div className="flex justify-center">
              <Form method="post" className="bg-[#1a2333] rounded-xl p-8 md:p-10 shadow-md border border-gray-100 max-w-3xl w-full text-white">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-white mb-1">
                    Full Name*
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    className={`w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#1e3a8a] focus:border-[#1e3a8a] text-center bg-white text-gray-800 ${
                      actionData?.errors?.name ? 'border-red-500' : ''
                    }`}
                  />
                  {actionData?.errors?.name && (
                    <p className="text-red-300 text-sm mt-1">{actionData.errors.name}</p>
                  )}
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-white mb-1">
                    Email Address*
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    className={`w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#1e3a8a] focus:border-[#1e3a8a] text-center bg-white text-gray-800 ${
                      actionData?.errors?.email ? 'border-red-500' : ''
                    }`}
                  />
                  {actionData?.errors?.email && (
                    <p className="text-red-300 text-sm mt-1">{actionData.errors.email}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-white mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#1e3a8a] focus:border-[#1e3a8a] text-center bg-white text-gray-800"
                  />
                </div>
                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-white mb-1">
                    Company/Organization Name*
                  </label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    required
                    className={`w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#1e3a8a] focus:border-[#1e3a8a] text-center bg-white text-gray-800 ${
                      actionData?.errors?.company ? 'border-red-500' : ''
                    }`}
                  />
                  {actionData?.errors?.company && (
                    <p className="text-red-300 text-sm mt-1">{actionData.errors.company}</p>
                  )}
                </div>
              </div>

              <div className="mb-6">
                <label htmlFor="businessType" className="block text-sm font-medium text-white mb-1">
                  Business Type*
                </label>
                <select
                  id="businessType"
                  name="businessType"
                  required
                  className={`w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#1e3a8a] focus:border-[#1e3a8a] text-center bg-white text-gray-800 ${
                    actionData?.errors?.businessType ? 'border-red-500' : ''
                  }`}
                >
                  <option value="">Select Business Type</option>
                  <option value="Coffee Shop">Coffee Shop</option>
                  <option value="Nonprofit">Nonprofit</option>
                  <option value="Retail Store">Retail Store</option>
                  <option value="Online Business">Online Business</option>
                  <option value="Fundraiser">Fundraiser</option>
                  <option value="Other">Other</option>
                </select>
                {actionData?.errors?.businessType && (
                  <p className="text-red-300 text-sm mt-1">{actionData.errors.businessType}</p>
                )}
              </div>

              <div className="mb-6">
                <label htmlFor="message" className="block text-sm font-medium text-white mb-1">
                  Tell us about your needs
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#1e3a8a] focus:border-[#1e3a8a] text-center bg-white text-gray-800"
                ></textarea>
              </div>

              <div className="text-center">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="inline-flex items-center px-6 py-3 bg-white text-[#1a2333] rounded-lg font-medium hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Inquiry'}
                  <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </button>
              </div>

              {actionData?.error && (
                <div className="mt-4 bg-red-900 border border-red-700 text-white px-4 py-3 rounded-lg">
                  {actionData.error}
                </div>
              )}
              </Form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
