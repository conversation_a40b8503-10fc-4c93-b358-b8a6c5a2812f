import {Link, useNavigate} from '@remix-run/react';
import {type MappedProductOptions} from '@shopify/hydrogen';
import type {
  Maybe,
  ProductOptionValueSwatch,
} from '@shopify/hydrogen/storefront-api-types';
import {AddToCartButton} from './AddToCartButton';
import {useAside} from './Aside';
import type {ProductFragment} from 'storefrontapi.generated';
import {useState} from 'react';
import {SubscriptionSelector, type SellingPlanFragment} from './SubscriptionSelector';
import {SubscriptionDropdown} from './SubscriptionDropdown';

export function ProductForm({
  productOptions,
  selectedVariant,
  product,
}: {
  productOptions: MappedProductOptions[];
  selectedVariant: ProductFragment['selectedOrFirstAvailableVariant'];
  product: ProductFragment;
}) {
  const navigate = useNavigate();
  const {open} = useAside();
  const [quantity, setQuantity] = useState(1);
  const [selectedSellingPlanId, setSelectedSellingPlanId] = useState<string | null>(null);

  return (
    <div className="product-form space-y-6">
      {/* Product Options */}
      {productOptions.map((option) => {
        // If there is only a single value in the option values, don't display the option
        if (option.optionValues.length === 1) return null;

        return (
          <div className="space-y-3" key={option.name}>
            <h3 className="text-sm font-medium text-gray-900">{option.name}</h3>
            <div className="flex flex-wrap gap-2">
              {option.optionValues.map((value) => {
                const {
                  name,
                  handle,
                  variantUriQuery,
                  selected,
                  available,
                  exists,
                  isDifferentProduct,
                  swatch,
                } = value;

                // Common class for all variant buttons/links
                const baseClasses = `px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                  selected
                    ? 'bg-[#1a2333] text-white shadow-sm'
                    : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                } ${!available || !exists ? 'opacity-40 cursor-not-allowed' : ''}`;

                if (isDifferentProduct) {
                  return (
                    <Link
                      className={baseClasses}
                      key={option.name + name}
                      prefetch="intent"
                      preventScrollReset
                      replace
                      to={`/products/${handle}?${variantUriQuery}`}
                    >
                      {swatch?.color ? (
                        <div className="flex items-center">
                          <span
                            className="w-4 h-4 rounded-full mr-2 border border-gray-200"
                            style={{backgroundColor: swatch.color}}
                          />
                          {name}
                        </div>
                      ) : (
                        name
                      )}
                    </Link>
                  );
                } else {
                  return (
                    <button
                      type="button"
                      className={baseClasses}
                      key={option.name + name}
                      disabled={!exists}
                      onClick={() => {
                        if (!selected) {
                          navigate(`?${variantUriQuery}`, {
                            replace: true,
                            preventScrollReset: true,
                          });
                        }
                      }}
                    >
                      {swatch?.color ? (
                        <div className="flex items-center">
                          <span
                            className="w-4 h-4 rounded-full mr-2 border border-gray-200"
                            style={{backgroundColor: swatch.color}}
                          />
                          {name}
                        </div>
                      ) : (
                        name
                      )}
                    </button>
                  );
                }
              })}
            </div>
          </div>
        );
      })}

      {/* Quantity Selector */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-900">Quantity</h3>
        <div className="flex items-center border border-gray-300 rounded-md w-32">
          <button
            type="button"
            className="px-3 py-1 text-gray-600 hover:text-gray-900 text-lg font-medium transition-colors"
            disabled={quantity <= 1}
            onClick={() => setQuantity(Math.max(1, quantity - 1))}
            aria-label="Decrease quantity"
          >
            −
          </button>
          <div className="flex-1 text-center">
            <span className="text-gray-900 font-medium">{quantity}</span>
          </div>
          <button
            type="button"
            className="px-3 py-1 text-gray-600 hover:text-gray-900 text-lg font-medium transition-colors"
            onClick={() => setQuantity(Math.min(10, quantity + 1))}
            aria-label="Increase quantity"
          >
            +
          </button>
        </div>
      </div>

      {/* Subscription Selector */}
      {product?.sellingPlanGroups?.nodes?.length > 0 && (
        <SubscriptionSelector
          product={product}
          selectedVariant={selectedVariant}
          onSellingPlanChange={setSelectedSellingPlanId}
        />
      )}

      {/* Stock Status */}
      <div className="flex items-center">
        {selectedVariant?.availableForSale ? (
          <>
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <span className="text-sm text-green-600 font-medium">In Stock</span>
          </>
        ) : (
          <>
            <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
            <span className="text-sm text-red-600 font-medium">Out of Stock</span>
          </>
        )}

        {/* SKU */}
        {selectedVariant?.sku && (
          <span className="ml-4 text-sm text-gray-500">
            SKU: {selectedVariant.sku}
          </span>
        )}
      </div>

      {/* Subscription Dropdown */}
      <SubscriptionDropdown
        product={product}
        selectedVariant={selectedVariant}
        quantity={quantity}
      />

      {/* Add to Cart Button */}
      <div className="pt-4">
        <AddToCartButton
          disabled={!selectedVariant || !selectedVariant.availableForSale}
          onClick={() => {
            open('cart');
          }}
          lines={
            selectedVariant
              ? [
                  {
                    merchandiseId: selectedVariant.id,
                    quantity: quantity,
                    selectedVariant,
                    ...(selectedSellingPlanId && {
                      sellingPlanId: selectedSellingPlanId,
                    }),
                  },
                ]
              : []
          }
          className="w-full bg-[#1a2333] hover:bg-[#2a3343] text-white py-4 px-6 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center shadow-sm"
        >
          <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
          {selectedVariant?.availableForSale ? 'Add to Cart' : 'Sold Out'}
        </AddToCartButton>
      </div>

      {/* Shipping & Returns */}
      <div className="flex items-center text-sm text-gray-500 mt-4">
        <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Ships within 1-2 business days
      </div>
    </div>
  );
}

// ProductOptionSwatch function removed as we've simplified the variant selection UI
