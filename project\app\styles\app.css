/* Cross-platform responsive scaling - replaces global font-size scaling */
html {
  font-size: 100%; /* Use browser default for better cross-platform compatibility */
}

/* Device pixel ratio adjustments for cross-platform compatibility */
@media screen and (-webkit-min-device-pixel-ratio: 2),
       screen and (min-resolution: 2dppx) {
  /* High DPI displays (Mac Retina, etc.) - scale down slightly */
  html {
    font-size: 90%;
  }

  /* Ensure images don't appear oversized on high DPI displays */
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  /* Adjust SVG icons for high DPI displays */
  svg {
    shape-rendering: geometricPrecision;
  }
}

@media screen and (-webkit-min-device-pixel-ratio: 3),
       screen and (min-resolution: 3dppx) {
  /* Very high DPI displays - scale down more */
  html {
    font-size: 85%;
  }

  /* Further optimize images for very high DPI displays */
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Page transition animation */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

main {
  animation: fadeIn 0.5s ease-in-out;
}

:root {
  /* Use relative units for better cross-platform compatibility */
  --aside-width: 28rem; /* ~450px at 16px base, but scales with font-size */
  --aside-width-mobile: 20rem; /* ~320px at 16px base */
  --cart-aside-summary-height-with-discount: 18.75rem; /* ~300px */
  --cart-aside-summary-height: 15.625rem; /* ~250px */
  --cart-aside-summary-height-mobile: 12.5rem; /* ~200px */
  --grid-item-width: 22.1875rem; /* ~355px */
  --header-height: 4.375rem; /* ~70px - using rem for better scaling */
  --color-dark: #000;
  --color-light: #fff;
  --color-slate-900: #1e293b;
  --color-slate-800: #1f2937;
  --color-gray-200: #e5e7eb;

  /* Icon and UI element sizing - relative units for cross-platform compatibility */
  --icon-size-sm: 1rem; /* ~16px */
  --icon-size-md: 1.5rem; /* ~24px */
  --icon-size-lg: 2rem; /* ~32px */
  --button-padding-y: 0.625rem; /* ~10px */
  --button-padding-x: 1rem; /* ~16px */

  /* Cross-platform button and UI sizing */
  --button-height-sm: 2rem; /* ~32px */
  --button-height-md: 2.5rem; /* ~40px */
  --button-height-lg: 3rem; /* ~48px */
}

main {
  padding-top: var(--header-height);
}

/* Add some global transitions */
.transform {
  transition-property: transform;
  transition-timing-function: var(--ease-default);
  transition-duration: var(--transition-normal);
}

img {
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

img.loaded, img.lazyloaded {
  opacity: 1;
}

/* Apply fade-in to all images after they load */
img {
  animation: imgFadeIn 0.8s ease-in-out forwards;
}

/* Cross-platform UI consistency */
button, input, select, textarea {
  /* Ensure consistent sizing across platforms */
  box-sizing: border-box;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* Ensure SVG icons scale properly across platforms */
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* Prevent images from appearing too large on high DPI displays - but only for images without specific styling */
img:not([class*="object-"]):not([style*="height"]):not([style*="object-fit"]) {
  max-width: 100%;
  height: auto;
  display: block;
}

@keyframes imgFadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

/* Video Intro Animations */
@keyframes videoFadeOut {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(1.05);
  }
}

@keyframes heroFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.video-intro-exit {
  animation: videoFadeOut 1s ease-out forwards;
}

.hero-enter {
  animation: heroFadeIn 1s ease-out forwards;
}

/*
* --------------------------------------------------
* Non anchor links
* --------------------------------------------------
*/
.link:hover {
  text-decoration: underline;
  cursor: pointer;
}

/*
* --------------------------------------------------
* components/Aside
* --------------------------------------------------
*/
@media (max-width: 45em) {
  html:has(.overlay.expanded) {
    overflow: hidden;
  }
}

aside {
  background: var(--color-light);
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);
  height: 100vh;
  width: min(var(--aside-width), 100vw);
  position: fixed;
  right: calc(-1 * var(--aside-width));
  top: 0;
  transition: transform 200ms ease-in-out;
  z-index: 61; /* Higher than overlay to ensure it's on top */
}

aside[aria-modal="mobile"] {
  background: var(--color-slate-900, #1e293b);
  color: var(--color-gray-200, #e5e7eb);
}

aside[aria-modal="mobile"] .close {
  color: var(--color-gray-200, #e5e7eb);
}

aside header {
  align-items: center;
  border-bottom: 1px solid var(--color-dark);
  display: flex;
  height: var(--header-height);
  justify-content: space-between;
  padding: 0 20px;
}

aside header h3 {
  margin: 0;
}

aside header .close {
  font-weight: bold;
  opacity: 0.8;
  text-decoration: none;
  transition: all 200ms;
  width: 20px;
}

aside header .close:hover {
  opacity: 1;
}

aside header h2 {
  margin-bottom: 0.6rem;
  margin-top: 0;
}

aside main {
  margin: 0;
  height: calc(100vh - var(--header-height));
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

aside p {
  margin: 0 0 0.25rem;
}

aside p:last-child {
  margin: 0;
}

aside li {
  margin-bottom: 0.125rem;
}

.overlay {
  background: rgba(0, 0, 0, 0.2);
  bottom: 0;
  left: 0;
  opacity: 0;
  pointer-events: none;
  position: fixed;
  right: 0;
  top: 0;
  transition: opacity 400ms ease-in-out;
  transition: opacity 400ms;
  visibility: hidden;
  z-index: 60; /* Higher than header z-index (50) */
}

.overlay .close-outside {
  background: transparent;
  border: none;
  color: transparent;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: calc(100% - var(--aside-width));
}

.overlay .light {
  background: rgba(255, 255, 255, 0.5);
}

.overlay .cancel {
  cursor: default;
  height: 100%;
  position: absolute;
  width: 100%;
}

.overlay.expanded {
  opacity: 1;
  pointer-events: auto;
  visibility: visible;
}
/* reveal aside */
.overlay.expanded aside {
  transform: translateX(calc(var(--aside-width) * -1));
}

button.reset {
  border: 0;
  background: inherit;
  font-size: inherit;
}

button.reset > * {
  margin: 0;
}

button.reset:not(:has(> *)) {
  height: 1.5rem;
  line-height: 1.5rem;
}

button.reset:hover:not(:has(> *)) {
  text-decoration: underline;
  cursor: pointer;
}

/*
* --------------------------------------------------
* components/Header
* --------------------------------------------------
*/
.header {
  align-items: center;
  background: #fff;
  display: flex;
  height: var(--header-height);
  padding: 0 1rem;
  position: sticky;
  top: 0;
  z-index: 1;
}

.header-menu-mobile-toggle {
  @media (min-width: 48em) {
    display: none;
  }
}

.header-menu-mobile a {
  color: var(--color-gray-200);
  transition: color 0.3s ease;
}

.header-menu-mobile a:hover {
  color: white;
}

.header-menu-mobile {
  display: flex;
  flex-direction: column;
  grid-gap: 1rem;
}

.header-menu-desktop {
  display: none;
  grid-gap: 1rem;
  @media (min-width: 45em) {
    display: flex;
    grid-gap: 1rem;
    margin-left: 3rem;
  }
}

.header-menu-item {
  cursor: pointer;
}

.header-nav-menu {
  background-color: var(--color-slate-800, #1f2937);
  text-align: center;
}

.header-nav-menu a {
  display: inline-block;
  padding: 0.75rem 1rem;
  font-weight: 500;
  letter-spacing: 0.05em;
}

.active-link {
  position: relative;
}

.active-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: white;
  transform-origin: bottom center;
  transform: scaleX(0.7);
  transition: transform 0.3s ease-in-out;
}

.active-link:hover::after {
  transform: scaleX(1);
}

.header-ctas {
  align-items: center;
  display: flex;
  grid-gap: 1rem;
  margin-left: auto;
}

.header-ctas > * {
  min-width: fit-content;
}


/*
* --------------------------------------------------
* components/Footer
* --------------------------------------------------
*/
.footer {
  /* Keeping this for compatibility */
  background: var(--color-slate-900, #1a2333);
  margin-top: auto;
}

/*
* --------------------------------------------------
* components/Cart
* --------------------------------------------------
*/
.cart-main {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  min-height: 0; /* Important for flex child to shrink */
}

.cart-main.with-discount {
  /* Remove max-height restrictions for better scrolling */
}

.cart-line {
  display: flex;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.cart-line:last-child {
  border-bottom: none;
}

.cart-line img {
  height: 100%;
  display: block;
  margin-right: 1rem;
  width: 80px;
  height: 80px;
  object-fit: cover;
}

.cart-summary-page {
  position: relative;
}

.cart-summary-aside {
  background: white;
  border-top: 1px solid var(--color-dark);
  padding: 1rem 1.5rem;
  flex-shrink: 0; /* Prevent summary from shrinking */
  margin-top: auto; /* Push to bottom */
}

.cart-line-quantity {
  display: flex;
}

.cart-discount {
  align-items: center;
  display: flex;
  margin-top: 0.25rem;
}

.cart-subtotal {
  align-items: center;
  display: flex;
}
/*
* --------------------------------------------------
* components/Search
* --------------------------------------------------
*/
.predictive-search {
  height: calc(100vh - var(--header-height) - 40px);
  overflow-y: auto;
}

.predictive-search-form {
  background: var(--color-light);
  position: sticky;
  top: 0;
  z-index: 10;
}

.predictive-search-result {
  margin-bottom: 2rem;
}

.predictive-search-result h5 {
  text-transform: uppercase;
}

.predictive-search-result-item {
  margin-bottom: 1rem;
}

.predictive-search-result-item a {
  align-items: center;
  display: flex;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
}

.predictive-search-result-item a:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.predictive-search-result-item a img {
  margin-right: 1rem;
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 0.25rem;
  flex-shrink: 0; /* Prevent image from shrinking */
}

.search-result {
  margin-bottom: 1.5rem;
}

.search-results-item {
  margin-bottom: 0.5rem;
}

.search-results-item a {
  display: flex;
  flex: row;
  align-items: center;
  gap: 1rem;
}

/*
* --------------------------------------------------
* routes/__index
* --------------------------------------------------
*/
.featured-collection {
  display: block;
  margin-bottom: 2rem;
  position: relative;
}

.featured-collection-image {
  aspect-ratio: 1 / 1;
  @media (min-width: 45em) {
    aspect-ratio: 16 / 9;
  }
}

.featured-collection img {
  height: auto;
  max-height: 100%;
  object-fit: cover;
}

.recommended-products-grid {
  display: grid;
  grid-gap: 1.5rem;
  grid-template-columns: repeat(2, 1fr);
  @media (min-width: 45em) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.recommended-product img {
  height: auto;
}

/*
* --------------------------------------------------
* routes/collections._index.tsx
* --------------------------------------------------
*/
.collections-grid {
  display: grid;
  grid-gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(var(--grid-item-width), 1fr));
  margin-bottom: 2rem;
}

.collection-item img {
  height: auto;
}

/*
* --------------------------------------------------
* routes/collections.$handle.tsx
* --------------------------------------------------
*/
.collection-description {
  margin-bottom: 1rem;
  max-width: 95%;
  @media (min-width: 45em) {
    max-width: 600px;
  }
}

.products-grid {
  display: grid;
  grid-gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(var(--grid-item-width), 1fr));
  margin-bottom: 2rem;
}

.product-item img {
  height: auto;
  width: 100%;
}

/*
* --------------------------------------------------
* routes/products.$handle.tsx
* --------------------------------------------------
*/
.product {
  display: grid;
  @media (min-width: 45em) {
    grid-template-columns: 1fr 1fr;
    grid-gap: 4rem;
  }
}

.product h1 {
  margin-top: 0;
}

.product-image img {
  height: auto;
  width: 100%;
}

.product-main {
  align-self: start;
  position: sticky;
  top: 6rem;
}

.product-price-on-sale {
  display: flex;
  grid-gap: 0.5rem;
}

.product-price-on-sale s {
  opacity: 0.5;
}

.product-options-grid {
  display: flex;
  flex-wrap: wrap;
  grid-gap: 0.75rem;
}

.product-options-item,
.product-options-item:disabled {
  padding: 0.25rem 0.5rem;
  background-color: transparent;
  font-size: 1rem;
  font-family: inherit;
}

.product-option-label-swatch {
  width: 1.25rem;
  height: 1.25rem;
  margin: 0.25rem 0;
}

.product-option-label-swatch img {
  width: 100%;
}

/*
* --------------------------------------------------
* routes/blog._index.tsx
* --------------------------------------------------
*/
.blog-grid {
  display: grid;
  grid-gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(var(--grid-item-width), 1fr));
  margin-bottom: 2rem;
}

.blog-article-image {
  aspect-ratio: 3/2;
  display: block;
}

.blog-article-image img {
  height: 100%;
}

/*
* --------------------------------------------------
* routes/blog.$articlehandle.tsx
* --------------------------------------------------
*/
.article img {
  height: auto;
  width: 100%;
}

/*
* --------------------------------------------------
* routes/account
* --------------------------------------------------
*/

.account-logout {
  display: inline-block;
}

/*
* --------------------------------------------------
* Mobile navigation menu fixes
* --------------------------------------------------
*/
@media (max-width: 768px) {
  /* Ensure mobile menu overlay is always on top of everything */
  .overlay {
    z-index: 99999 !important;
  }

  /* Ensure mobile menu aside is on top of overlay */
  aside[aria-modal="mobile"] {
    z-index: 100000 !important;
  }

  /* Prevent body scroll when mobile menu is open */
  body:has(.overlay.expanded) {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  /* Ensure mobile menu header is properly styled */
  aside[aria-modal="mobile"] header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: var(--color-slate-900, #1e293b);
  }

  /* Improve mobile menu visibility */
  aside[aria-modal="mobile"] nav {
    background: var(--color-slate-900, #1e293b);
  }

  /* Make all asides smaller on mobile */
  aside {
    width: min(var(--aside-width-mobile), 90vw) !important;
    right: calc(-1 * var(--aside-width-mobile)) !important;
  }

  /* Adjust overlay close area for smaller mobile aside */
  .overlay .close-outside {
    width: calc(100% - var(--aside-width-mobile)) !important;
  }

  /* Adjust cart summary for mobile */
  .cart-summary-aside {
    width: calc(var(--aside-width-mobile) - 40px) !important;
    min-height: 220px !important; /* Ensure enough space for checkout button */
    position: sticky !important;
    bottom: 0 !important;
    background: white !important;
    z-index: 10 !important;
  }

  /* Mobile cart optimizations - compact layout */
  .cart-summary-aside {
    padding: 0.5rem !important;
    margin-top: 0 !important;
    background: white !important;
    border-top: 1px solid #e5e7eb !important;
  }

  /* Ensure proper scrolling on mobile */
  .cart-main {
    padding: 0.5rem !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    max-height: calc(100vh - 280px) !important; /* Increased space for checkout button */
  }

  /* Adjust aside transform for mobile width */
  .overlay.expanded aside {
    transform: translateX(calc(var(--aside-width-mobile) * -1)) !important;
  }

  /* Optimize aside content spacing for mobile */
  aside main {
    height: calc(100vh - 60px) !important; /* Full height minus header */
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
  }

  /* Optimize aside header for mobile */
  aside header {
    padding: 0 0.5rem !important;
    height: 60px !important;
  }

  aside header h3 {
    font-size: 1.1rem !important;
  }

  /* Optimize cart line items for mobile */
  .cart-line {
    padding: 0.5rem 0 !important;
  }

  .cart-line img {
    width: 50px !important;
    height: 50px !important;
    margin-right: 0.5rem !important;
  }

  /* Make cart text more compact */
  .cart-line h3 {
    font-size: 0.8rem !important;
    line-height: 1.2 !important;
    margin-bottom: 0.25rem !important;
  }

  .cart-line .price {
    font-size: 0.75rem !important;
  }

  /* Optimize cart quantity controls */
  .cart-line-quantity {
    gap: 0.25rem !important;
  }

  .cart-line-quantity button {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.7rem !important;
  }

  /* Compact cart summary */
  .cart-summary-aside h4 {
    font-size: 1rem !important;
    margin-bottom: 0.5rem !important;
  }

  .cart-summary-aside dl {
    margin-bottom: 0.5rem !important;
  }

  .cart-summary-aside button {
    padding: 0.75rem !important;
    font-size: 0.9rem !important;
    margin-bottom: 0.5rem !important; /* Ensure button isn't cut off */
  }
}

/*
* --------------------------------------------------
* Mobile-only product card optimizations
* --------------------------------------------------
*/
@media (max-width: 480px) {
  /* Optimize spacing for 2-column mobile layout */
  .grid.gap-6 {
    gap: 0.75rem !important;
  }

  /* Optimize product card content for mobile */
  .product-item, .recommended-product {
    max-width: 100%;
  }

  /* Optimize product images for mobile 2-column layout */
  .product-item img, .recommended-product img {
    max-height: 140px !important;
    width: 100%;
    object-fit: cover;
  }

  /* Adjust typography for mobile 2-column layout */
  .product-item h3,
  .recommended-product h3 {
    font-size: 0.875rem !important;
    line-height: 1.2 !important;
    margin-bottom: 0.5rem !important;
  }

  /* Optimize button sizing for mobile */
  .product-item button,
  .recommended-product button {
    padding: 0.6rem 0.8rem !important;
    font-size: 0.8rem !important;
  }

  /* Optimize price display */
  .product-item .price,
  .recommended-product .price {
    font-size: 0.875rem !important;
  }

  /* Optimize add to cart button layout for mobile */
  .flex.gap-2 {
    gap: 0.5rem !important;
  }

  /* Ensure proper spacing in product cards */
  .product-item .p-5,
  .recommended-product .p-5 {
    padding: 0.75rem !important;
  }

  /* Fix add to cart buttons on mobile product cards */
  .product-item .bg-\[#1a2333\],
  .recommended-product .bg-\[#1a2333\] {
    padding: 0.6rem 0.8rem !important;
    font-size: 0.8rem !important;
  }
}

/*
* --------------------------------------------------
* Targeted mobile fixes that work with global zoom
* --------------------------------------------------
*/

/* Hero image enlargement - works with global zoom */
.hero-section .absolute.inset-0.z-0 {
  margin: -1rem !important;
}

.hero-section .absolute.inset-0.z-0 > div {
  transform: scale(1.15) !important;
}

/* Mobile hero image - remove borders and make larger */
@media (max-width: 768px) {
  .hero-section {
    margin: -1rem !important;
    padding: 1rem !important;
  }

  .hero-section > div:first-child {
    margin: -3rem !important;
    transform: scale(1.2) !important;
  }
}

/* Mobile hero buttons - smaller on mobile */
@media (max-width: 640px) {
  .hero-buttons a {
    padding: 0.5rem 1rem !important;
    min-width: auto !important;
    width: auto !important;
    display: inline-block !important;
    font-size: 0.875rem !important;
    max-width: 150px !important;
  }
}