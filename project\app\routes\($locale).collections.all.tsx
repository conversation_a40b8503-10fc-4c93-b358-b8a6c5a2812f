import {type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {useLoaderData, type MetaFunction, useSearchParams} from '@remix-run/react';
import {getPaginationVariables} from '@shopify/hydrogen';
import {EnhancedProductGrid} from '~/components/EnhancedProductGrid';
import {EnhancedProductFilters} from '~/components/EnhancedProductFilters';
import {MobileFilterDrawer} from '~/components/MobileFilterDrawer';
import {CollectionHeader} from '~/components/CollectionHeader';
import {useState} from 'react';

export const meta: MetaFunction<typeof loader> = () => {
  return [{title: `The Good Coffee Company | Shop All Coffee`}];
};

export async function loader(args: LoaderFunctionArgs) {
  const {request, context} = args;
  const {storefront} = context;
  const searchParams = new URL(request.url).searchParams;

  const paginationVariables = getPaginationVariables(request, {
    pageBy: 24,
  });

  // Build query string for filtering
  let queryString = '';

  // Add coffee type filter
  const coffeeType = searchParams.get('type');
  if (coffeeType && coffeeType !== 'all') {
    queryString += `tag:'${coffeeType}' `;
  }

  // Add roast filter
  const roast = searchParams.get('roast');
  if (roast && roast !== 'all') {
    queryString += `tag:'${roast}-roast' `;
  }

  // Add origin filter
  const origin = searchParams.get('origin');
  if (origin) {
    queryString += `tag:'origin-${origin}' `;
  }

  // Add price filter
  const minPrice = searchParams.get('minPrice');
  const maxPrice = searchParams.get('maxPrice');
  if (minPrice || maxPrice) {
    if (minPrice) queryString += `variants.price:>=${minPrice} `;
    if (maxPrice) queryString += `variants.price:<=${maxPrice} `;
  }

  // Get sort parameter
  const sortKey = searchParams.get('sort');
  let sortVariable = 'BEST_SELLING'; // Default sort

  if (sortKey) {
    switch (sortKey) {
      case 'price-asc':
        sortVariable = 'PRICE';
        break;
      case 'price-desc':
        sortVariable = 'PRICE';
        // We'll handle the reverse in the query
        break;
      case 'title-asc':
        sortVariable = 'TITLE';
        break;
      case 'title-desc':
        sortVariable = 'TITLE';
        // We'll handle the reverse in the query
        break;
      case 'newest':
        sortVariable = 'CREATED';
        break;
      default:
        sortVariable = 'BEST_SELLING';
    }
  }

  // Determine if we need to reverse the sort order
  const reverse = sortKey === 'price-desc' || sortKey === 'title-desc';

  const [{products}] = await Promise.all([
    storefront.query(CATALOG_QUERY, {
      variables: {
        ...paginationVariables,
        query: queryString.trim() || null,
        sortKey: sortVariable,
        reverse,
      },
    }),
  ]);

  return {
    products,
    appliedFilters: {
      coffeeType: coffeeType || 'all',
      roast: roast || 'all',
      origin: origin || '',
      minPrice,
      maxPrice,
      sort: sortKey || 'best-selling',
    },
  };
}

export default function Collection() {
  const {products, appliedFilters} = useLoaderData<typeof loader>();
  const [searchParams] = useSearchParams();
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);

  // Count active filters
  const activeFiltersCount = [
    appliedFilters.coffeeType !== 'all' ? 1 : 0,
    appliedFilters.roast !== 'all' ? 1 : 0,
    appliedFilters.origin ? 1 : 0,
    appliedFilters.minPrice || appliedFilters.maxPrice ? 1 : 0,
  ].reduce((a, b) => a + b, 0);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <CollectionHeader
        title="All Coffee Products"
        description="Discover our premium selection of coffee beans, sourced from the finest growing regions around the world."
        productCount={products?.nodes?.length || 0}
        handle="all"
      />

      {/* Mobile filter button */}
      <div className="lg:hidden mb-6">
        <button
          type="button"
          className="flex items-center justify-center w-full py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
          onClick={() => setIsFilterDrawerOpen(true)}
        >
          <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 010 2H4a1 1 0 01-1-1zm0 8a1 1 0 011-1h16a1 1 0 010 2H4a1 1 0 01-1-1zm0 8a1 1 0 011-1h16a1 1 0 010 2H4a1 1 0 01-1-1z" />
          </svg>
          Filters
          {activeFiltersCount > 0 && (
            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
              {activeFiltersCount}
            </span>
          )}
        </button>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Desktop Filters */}
        <div className="hidden lg:block lg:w-64">
          <EnhancedProductFilters />
        </div>

        {/* Product grid */}
        <div className="flex-1">
          {!products?.nodes?.length ? (
            <div className="text-center py-12 bg-white rounded-lg shadow-sm">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h2 className="mt-4 text-lg font-medium text-gray-900">No products found</h2>
              <p className="mt-2 text-sm text-gray-500">Try adjusting your filters or search terms</p>
            </div>
          ) : (
            <EnhancedProductGrid
              products={products.nodes}
              title=""
              enableFiltering={false}
              enableSorting={true}
              columnsDesktop={3}
              columnsMobile={1}
              highlightNewest={true}
            />
          )}
        </div>
      </div>

      {/* Mobile filter drawer */}
      <MobileFilterDrawer
        isOpen={isFilterDrawerOpen}
        onClose={() => setIsFilterDrawerOpen(false)}
        activeFiltersCount={activeFiltersCount}
      />
    </div>
  );
}

const CATALOG_QUERY = `#graphql
  fragment MoneyFragment on MoneyV2 {
    amount
    currencyCode
  }

  query Catalog(
    $country: CountryCode
    $language: LanguageCode
    $first: Int
    $last: Int
    $startCursor: String
    $endCursor: String
    $query: String
    $sortKey: ProductSortKeys
    $reverse: Boolean
  ) @inContext(country: $country, language: $language) {
    products(
      first: $first,
      last: $last,
      before: $startCursor,
      after: $endCursor,
      sortKey: $sortKey,
      reverse: $reverse,
      query: $query
    ) {
      nodes {
        id
        title
        handle
        vendor
        availableForSale
        tags
        createdAt

        featuredImage {
          id
          altText
          url
          width
          height
        }

        priceRange {
          minVariantPrice {
            ...MoneyFragment
          }
          maxVariantPrice {
            ...MoneyFragment
          }
        }

        variants(first: 1) {
          nodes {
            id
            availableForSale
            selectedOptions {
              name
              value
            }
            price {
              ...MoneyFragment
            }
            compareAtPrice {
              ...MoneyFragment
            }
          }
        }
      }
      pageInfo {
        hasPreviousPage
        hasNextPage
        startCursor
        endCursor
      }
    }
  }
` as const;