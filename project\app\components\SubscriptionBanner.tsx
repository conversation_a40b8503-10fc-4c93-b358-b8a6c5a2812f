import {Link} from '@remix-run/react';

export function SubscriptionBanner() {
  return (
    <div className="bg-[#1a2333] text-white rounded-lg overflow-hidden shadow-xl my-12">
      <div className="relative">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <pattern
                id="coffee-pattern"
                patternUnits="userSpaceOnUse"
                width="20"
                height="20"
                patternTransform="rotate(45)"
              >
                <circle cx="10" cy="10" r="2" fill="currentColor" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#coffee-pattern)" />
          </svg>
        </div>

        <div className="relative px-8 py-12 md:px-12 md:py-16 flex flex-col md:flex-row items-center">
          {/* Left content */}
          <div className="flex-1 text-center md:text-left mb-8 md:mb-0">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Roasters Choice
            </h2>
            <p className="text-xl opacity-90 mb-6">
              Receive 3 bags of our premium, fresh, low acid, organic coffee delivered to your door on your schedule.
            </p>
            <ul className="space-y-3 mb-8 md:mb-0 text-base md:text-lg">
              <li className="flex items-center justify-center md:justify-start">
                <svg className="w-6 h-6 mr-3 text-green-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Curated by our coffee experts
              </li>
              <li className="flex items-center justify-center md:justify-start">
                <svg className="w-6 h-6 mr-3 text-green-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Discover new favorites every month
              </li>
              <li className="flex items-center justify-center md:justify-start">
                <svg className="w-6 h-6 mr-3 text-green-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Cancel or modify anytime
              </li>
            </ul>
          </div>

          {/* Right content */}
          <div className="flex flex-col items-center md:items-end">
            <div className="text-center md:text-right mb-6">
              {/* Summer Special Badge */}
              <div className="inline-block bg-red-500 text-white text-sm font-bold px-3 py-1 rounded-full mb-3">
                SUMMER SPECIAL
              </div>
              {/* Pricing */}
              <div className="flex flex-col items-center md:items-end">
                <div className="text-lg text-gray-300 line-through mb-1">
                  $59.97
                </div>
                <div className="text-4xl font-bold text-green-400">
                  $40
                </div>
              </div>
              <div className="text-base opacity-70 mt-2">
                Free shipping on all subscription orders
              </div>
            </div>
            <Link
              to="/products/coffee-subscription"
              className="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-md shadow-md text-white bg-[#1a2333] hover:bg-[#2a3343] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white focus:ring-offset-[#1a2333] transition-colors"
            >
              Subscribe Now
              <svg className="ml-2 -mr-1 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
