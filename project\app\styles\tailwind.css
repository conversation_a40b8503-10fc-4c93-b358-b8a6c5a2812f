@import "tailwindcss";

:root {
  /* Modern color system using oklch */
  --color-primary-50: oklch(0.98 0.02 255);
  --color-primary-100: oklch(0.96 0.03 255);
  --color-primary-200: oklch(0.92 0.05 255);
  --color-primary-300: oklch(0.88 0.07 255);
  --color-primary-400: oklch(0.84 0.09 255);
  --color-primary-500: oklch(0.80 0.11 255);
  --color-primary-600: oklch(0.75 0.13 255);
  --color-primary-700: oklch(0.70 0.15 255);
  --color-primary-800: oklch(0.65 0.17 255);
  --color-primary-900: oklch(0.60 0.19 255);

  --color-gray-50: oklch(0.98 0.005 255);
  --color-gray-100: oklch(0.95 0.005 255);
  --color-gray-200: oklch(0.90 0.005 255);
  --color-gray-300: oklch(0.85 0.005 255);
  --color-gray-400: oklch(0.80 0.005 255);
  --color-gray-500: oklch(0.75 0.005 255);
  --color-gray-600: oklch(0.65 0.005 255);
  --color-gray-700: oklch(0.55 0.005 255);
  --color-gray-800: oklch(0.45 0.005 255);
  --color-gray-900: oklch(0.35 0.005 255);

  /* Spacing system */
  --spacing: 0.25rem;

  /* Typography */
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;

  /* Transitions */
  --transition-fast: 150ms;
  --transition-normal: 250ms;
  --transition-slow: 350ms;
  --ease-default: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

@layer base {
  html {
    font-family: var(--font-sans);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    background-color: var(--color-gray-50);
    color: var(--color-gray-900);
  }
}

@layer components {
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: calc(var(--spacing) * 2) calc(var(--spacing) * 4);
    font-weight: 500;
    border-radius: 0.375rem;
    transition-property: all;
    transition-timing-function: var(--ease-default);
    transition-duration: var(--transition-normal);
  }

  .btn-primary {
    background-color: var(--color-primary-600);
    color: white;
  }

  .btn-primary:hover {
    background-color: var(--color-primary-700);
  }

  .card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: var(--shadow);
    transition: box-shadow var(--transition-normal) var(--ease-default);
  }

  .card:hover {
    box-shadow: var(--shadow-md);
  }

  .input {
    width: 100%;
    padding: calc(var(--spacing) * 2);
    border: 1px solid var(--color-gray-200);
    border-radius: 0.375rem;
    background-color: white;
    transition: border-color var(--transition-fast) var(--ease-default);
  }

  .input:focus {
    outline: none;
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 2px color-mix(in oklch, var(--color-primary-500) 25%, transparent);
  }
}

@layer utilities {
  .container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: calc(var(--spacing) * 4);
    padding-right: calc(var(--spacing) * 4);
    max-width: 80rem;
  }

  .prose {
    max-width: 65ch;
    line-height: 1.75;
  }

  .prose p {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
  }

  .animate-fade {
    animation: fade var(--transition-normal) var(--ease-default);
  }

  .animate-slide-up {
    animation: slide-up 0.5s ease-out;
  }

  .animate-pulse-subtle {
    animation: pulse-subtle 3s infinite;
  }

  .animate-subtle-zoom {
    animation: subtle-zoom 15s ease-in-out infinite alternate;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  @keyframes fade {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse-subtle {
    0% {
      box-shadow: 0 0 0 0 rgba(var(--color-primary-500), 0.4);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(var(--color-primary-500), 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(var(--color-primary-500), 0);
    }
  }

  @keyframes subtle-zoom {
    0% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1.15);
    }
  }
}
