import {useLoaderD<PERSON>, <PERSON>, type MetaFunction} from '@remix-run/react';
import {Image, Money} from '@shopify/hydrogen';
import {useState} from 'react';
import {AddToCartButton} from '~/components/AddToCartButton';
import {useAside} from '~/components/Aside';

export const meta: MetaFunction = () => {
  return [{title: 'Coffee Subscription | The Good Coffee Company'}];
};

export default function MysterySubscriptionPage() {
  const [selectedFrequency, setSelectedFrequency] = useState('monthly');
  const [quantity, setQuantity] = useState(1);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const {open} = useAside();

  // Subscription options
  const frequencies = [
    { id: 'weekly', name: 'Weekly', description: 'Delivered every week', price: 46.95, compareAtPrice: 59.99, sellingPlanId: 'gid://shopify/SellingPlan/4367712435' },
    { id: 'monthly', name: 'Monthly', description: 'Delivered every month', price: 46.95, compareAtPrice: 59.99, sellingPlanId: 'gid://shopify/SellingPlan/4367843507', savings: 'Most Popular' },
    { id: 'every3weeks', name: 'Every 3 Weeks', description: 'Delivered every 3 weeks', price: 46.95, compareAtPrice: 59.99, sellingPlanId: 'gid://shopify/SellingPlan/4449337523' },
    { id: 'every6weeks', name: 'Every 6 Weeks', description: 'Delivered every 6 weeks', price: 46.95, compareAtPrice: 59.99, sellingPlanId: 'gid://shopify/SellingPlan/4449370291' },
  ];

  const selectedOption = frequencies.find(f => f.id === selectedFrequency) || frequencies[0];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Breadcrumbs */}
      <nav className="flex mb-8 text-sm">
        <Link to="/" className="text-gray-500 hover:text-gray-700">Home</Link>
        <span className="mx-2 text-gray-400">/</span>
        <Link to="/buy" className="text-gray-500 hover:text-gray-700">Products</Link>
        <span className="mx-2 text-gray-400">/</span>
        <span className="text-gray-900">Mystery Coffee Subscription</span>
      </nav>

      {/* Product Layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
        {/* Product Images */}
        <div className="space-y-6">
          <div className="aspect-w-1 aspect-h-1 bg-gray-100 rounded-lg overflow-hidden">
            <img
              src="/medium_pretty_bag.webp"
              alt="Mystery Coffee Subscription"
              className="w-full h-full object-center object-cover"
            />
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div className="aspect-w-1 aspect-h-1 bg-gray-100 rounded-lg overflow-hidden">
              <img
                src="/table_coffee_bag.webp"
                alt="Coffee bag on table"
                className="w-full h-full object-center object-cover"
              />
            </div>
            <div className="aspect-w-1 aspect-h-1 bg-gray-100 rounded-lg overflow-hidden">
              <img
                src="/brewing_3.webp"
                alt="Coffee brewing"
                className="w-full h-full object-center object-cover"
              />
            </div>
            <div className="aspect-w-1 aspect-h-1 bg-gray-100 rounded-lg overflow-hidden">
              <img
                src="/medium_pretty_bag.webp"
                alt="Coffee bag"
                className="w-full h-full object-center object-cover"
              />
            </div>
          </div>
        </div>

        {/* Product Details */}
        <div>
          <div className="mb-6">
            <p className="text-gray-500 mb-2 uppercase tracking-wider text-sm">The Good Coffee Company</p>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900 leading-tight">Coffee Subscription - 3 Bags</h1>
          </div>

          {/* Price */}
          <div className="text-xl font-bold mb-8">
            <div className="flex items-center">
              <span className="text-2xl">${selectedOption.price.toFixed(2)}</span>
              {selectedOption.compareAtPrice && (
                <span className="ml-2 text-gray-500 line-through text-lg">${selectedOption.compareAtPrice.toFixed(2)}</span>
              )}
              <span className="ml-3 bg-green-100 text-green-800 text-xs font-semibold px-2.5 py-0.5 rounded">Save 20%</span>
            </div>
          </div>

          {/* Description */}
          <div className="prose prose-sm mb-8">
            <p>
              Enjoy the convenience of our Coffee Subscription. Each delivery, we'll send you 3 bags of our premium, fresh, low acid, mold free coffee, carefully selected by our coffee experts.
            </p>
            <p>
              This is a perfect way to ensure you never run out of your favorite coffee. Each delivery brings you our premium coffee with the convenience of automatic delivery on your preferred schedule.
            </p>
            <ul>
              <li>3 bags of premium, fresh, mold free coffee (12oz each)</li>
              <li>Selected from our quality, low acid offerings</li>
              <li>Freshly roasted before shipping</li>
              <li>Delivered on your schedule</li>
              <li>Free shipping on all subscription orders</li>
            </ul>
          </div>

          {/* Subscription Options */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Delivery Frequency
              </h3>
              <span className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">
                Free Shipping
              </span>
            </div>

            {/* Dropdown Selector */}
            <div className="relative">
              <button
                type="button"
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-left shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400 transition-colors duration-200"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-900">{selectedOption.name}</span>
                      {selectedOption.savings && (
                        <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full font-medium">
                          {selectedOption.savings}
                        </span>
                      )}
                    </div>
                    <span className="text-sm text-gray-500">{selectedOption.description}</span>
                  </div>
                  <svg
                    className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
                      isDropdownOpen ? 'transform rotate-180' : ''
                    }`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </button>

              {/* Dropdown Options */}
              {isDropdownOpen && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
                  {frequencies.map((frequency) => (
                    <button
                      key={frequency.id}
                      type="button"
                      onClick={() => {
                        setSelectedFrequency(frequency.id);
                        setIsDropdownOpen(false);
                      }}
                      className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 first:rounded-t-lg last:rounded-b-lg ${
                        selectedFrequency === frequency.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-center">
                            <span className={`font-medium ${
                              selectedFrequency === frequency.id ? 'text-blue-900' : 'text-gray-900'
                            }`}>
                              {frequency.name}
                            </span>
                            {frequency.savings && (
                              <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full font-medium">
                                {frequency.savings}
                              </span>
                            )}
                          </div>
                          <span className={`text-sm ${
                            selectedFrequency === frequency.id ? 'text-blue-700' : 'text-gray-500'
                          }`}>
                            {frequency.description}
                          </span>
                        </div>
                        {selectedFrequency === frequency.id && (
                          <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Quantity Selector */}
          <div className="mb-8">
            <h3 className="text-sm font-medium text-gray-900 mb-4">Quantity</h3>
            <div className="flex items-center border border-gray-300 rounded-md w-32">
              <button
                type="button"
                className="px-3 py-1 text-gray-600 hover:text-gray-900 text-lg font-medium transition-colors"
                disabled={quantity <= 1}
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                aria-label="Decrease quantity"
              >
                −
              </button>
              <div className="flex-1 text-center">
                <span className="text-gray-900 font-medium">{quantity}</span>
              </div>
              <button
                type="button"
                className="px-3 py-1 text-gray-600 hover:text-gray-900 text-lg font-medium transition-colors"
                onClick={() => setQuantity(Math.min(10, quantity + 1))}
                aria-label="Increase quantity"
              >
                +
              </button>
            </div>
          </div>

          {/* Add to Cart Button */}
          <div className="pt-4">
            <AddToCartButton
              onClick={() => {
                open('cart');
              }}
              lines={[
                {
                  merchandiseId: 'gid://shopify/ProductVariant/44710840860851',
                  quantity: quantity,
                  sellingPlanId: selectedOption.sellingPlanId,
                },
              ]}
              className="w-full bg-[#1a2333] hover:bg-[#2a3343] text-white py-4 px-6 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center shadow-sm"
            >
              <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
              Subscribe Now
            </AddToCartButton>

            <div className="mt-3 text-center text-sm text-gray-600 flex items-center justify-center">
              <svg className="w-4 h-4 mr-1 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>A customer account is required for subscription purchases</span>
            </div>
          </div>

          {/* Subscription Details */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Subscription Details</h3>
            <ul className="text-sm text-gray-600 space-y-2">
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Cancel or modify your subscription anytime</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Skip deliveries when you need to</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Free shipping on all subscription orders</span>
              </li>
            </ul>
          </div>

          {/* How to Manage Your Subscription */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-sm font-medium text-blue-900 mb-2">How to Manage Your Subscription</h3>
            <p className="text-sm text-blue-800 mb-3">
              Managing your subscription is simple. After subscribing, you'll have two easy ways to manage your subscription:
            </p>
            <ul className="text-sm text-blue-800 space-y-4 mb-3">
              <li>
                <h4 className="font-medium">Option 1: Via Email</h4>
                <div className="flex items-start mt-1">
                  <svg className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span>You'll receive order confirmation and subscription management emails from The Good Coffee Company</span>
                </div>
                <div className="flex items-start mt-1">
                  <svg className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <span>Click the "Manage Subscription" link in these emails to access your subscription controls</span>
                </div>
              </li>

              <li>
                <h4 className="font-medium">Option 2: Via Your Account</h4>
                <div className="flex items-start mt-1">
                  <svg className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <span>Log in to your customer account at thegoodcoffeeco.com</span>
                </div>
                <div className="flex items-start mt-1">
                  <svg className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  <span>Navigate to your subscription management section</span>
                </div>
              </li>
            </ul>

            <h4 className="font-medium text-blue-900 mb-1">What You Can Manage:</h4>
            <ul className="ml-7 mb-3 space-y-1 text-blue-800">
              <li>• Change delivery frequency</li>
              <li>• Skip upcoming deliveries</li>
              <li>• Update payment methods</li>
              <li>• Change shipping address</li>
              <li>• Cancel your subscription</li>
            </ul>

            <p className="text-sm text-blue-800 italic">
              Note: You'll need the customer account created during checkout to manage your subscription.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
