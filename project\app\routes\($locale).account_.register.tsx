import {
  json,
  redirect,
  type ActionFunctionArgs,
  type LoaderFunctionArgs,
} from '@shopify/remix-oxygen';
import {Form, Link, useActionData, useNavigation, type MetaFunction} from '@remix-run/react';
import {useState} from 'react';

export const meta: MetaFunction = () => {
  return [{title: 'Register'}];
};

export async function loader({request, context}: LoaderFunctionArgs) {
  // If the user is already logged in, redirect to the account page
  const isLoggedIn = await context.customerAccount.isLoggedIn();
  if (isLoggedIn) {
    return redirect('/account');
  }

  // Get the URL params
  const url = new URL(request.url);
  const error = url.searchParams.get('error');

  return json({error});
}

export async function action({request, context}: ActionFunctionArgs) {
  // For now, just redirect to the Shopify login page
  // In a real implementation, we would handle the registration process here
  // and then redirect to the login page
  return context.customerAccount.login({
    callbackPath: '/account/authorize',
  });
}

export default function Register() {
  const actionData = useActionData<{error: string | null}>();
  const navigation = useNavigation();
  const [showPassword, setShowPassword] = useState(false);

  // Check if we're submitting the guest form
  const isGuestSubmitting = navigation.state === 'submitting' &&
    navigation.formData?.get('_action') === 'guest';

  // Check if we're submitting the register form
  const isRegisterSubmitting = navigation.state === 'submitting' &&
    navigation.formData?.get('_action') !== 'guest';

  return (
    <div className="flex min-h-[calc(100vh-200px)] items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="w-full max-w-md space-y-6 bg-white p-8 rounded-lg shadow-lg border border-gray-100 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#1a2333] to-[#2a3749]"></div>
        <div className="absolute -top-24 -right-24 w-48 h-48 rounded-full bg-gray-50 opacity-20"></div>
        <div className="absolute -bottom-16 -left-16 w-32 h-32 rounded-full bg-gray-50 opacity-20"></div>

        {/* Header */}
        <div className="text-center relative">
          <div className="mb-4 flex justify-center">
            <svg className="w-12 h-12 text-[#1a2333]" fill="currentColor" viewBox="0 0 24 24">
              <path d="M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm-9-2V7H4v3H1v2h3v3h2v-3h3v-2H6zm9 4c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"></path>
            </svg>
          </div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 mb-1">
            Create an Account
          </h1>
          <p className="text-sm text-gray-600 mb-6">
            Join The Good Coffee Company community
          </p>
        </div>

        {/* Error message */}
        {actionData?.error && (
          <div className="rounded-md bg-red-50 p-4 mb-6 border-l-4 border-red-400">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-red-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  {actionData.error}
                </h3>
              </div>
            </div>
          </div>
        )}

        {/* Registration Form */}
        <Form method="post" className="space-y-4">
          <div>
            <button
              type="submit"
              className="group relative flex w-full justify-center rounded-md bg-[#1a2333] px-4 py-3.5 text-sm font-semibold text-white hover:bg-[#2a3749] focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[#1a2333] transition-all duration-200 shadow-sm"
            >
              <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                <svg
                  className="h-5 w-5 text-gray-300 group-hover:text-white"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 1a4.5 4.5 0 00-4.5 4.5V9H5a2 2 0 00-2 2v6a2 2 0 002 2h10a2 2 0 002-2v-6a2 2 0 00-2-2h-.5V5.5A4.5 4.5 0 0010 1zm3 8V5.5a3 3 0 10-6 0V9h6z"
                    clipRule="evenodd"
                  />
                </svg>
              </span>
              Register with Shopify
            </button>
          </div>
        </Form>

        {/* Divider */}
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-200" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="bg-white px-4 text-gray-500">Or</span>
          </div>
        </div>

        {/* Guest Checkout */}
        <Form method="post" action="/account/login" className="space-y-4">
          <input type="hidden" name="_action" value="guest" />
          <button
            type="submit"
            disabled={isGuestSubmitting}
            className="group relative flex w-full justify-center rounded-md bg-gray-100 px-4 py-3.5 text-sm font-semibold text-gray-800 hover:bg-gray-200 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-400 transition-all duration-200 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-gray-100"
          >
            <span className="absolute inset-y-0 left-0 flex items-center pl-3">
              {isGuestSubmitting ? (
                <svg className="animate-spin h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg
                  className="h-5 w-5 text-gray-500 group-hover:text-gray-700"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path d="M10 8a3 3 0 100-6 3 3 0 000 6zM3.465 14.493a1.23 1.23 0 00.41 1.412A9.957 9.957 0 0010 18c2.31 0 4.438-.784 6.131-2.1.43-.333.604-.903.408-1.41a7.002 7.002 0 00-13.074.003z" />
                </svg>
              )}
            </span>
            {isGuestSubmitting ? 'Continuing...' : 'Continue as Guest'}
          </button>
        </Form>

        {/* Sign in link */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-600">
            Already have an account?{' '}
            <Link
              to="/account/login"
              className="font-medium text-[#1a2333] hover:text-[#2a3749] transition-colors"
            >
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
