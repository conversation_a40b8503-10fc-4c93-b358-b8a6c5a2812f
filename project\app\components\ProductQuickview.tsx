import { useEffect } from 'react';
import { Link } from '@remix-run/react';
import { Image, Money } from '@shopify/hydrogen';
import type { ProductFragment } from 'storefrontapi.generated';

interface ProductQuickviewProps {
  product: any;
  isOpen: boolean;
  onClose: () => void;
}

export function ProductQuickview({ product, isOpen, onClose }: ProductQuickviewProps) {

  // Handle escape key press to close modal
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, onClose]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen || !product) return null;

  // Get product data
  const image = product.featuredImage || product.variants?.nodes[0]?.image;
  const price = product.variants?.nodes[0]?.price;
  const compareAtPrice = product.variants?.nodes[0]?.compareAtPrice;
  const isOnSale = compareAtPrice?.amount > price?.amount;

  // Generate custom product description based on product title
  const getCustomDescription = (title: string) => {
    const titleLower = title.toLowerCase();

    if (titleLower.includes('colombia') || titleLower.includes('colombian')) {
      return "Savor the gentle sweetness of caramel, cherry, and a hint of cocoa. This fresh, low acid, mold free roast showcases a comforting harmony of flavors that leave a lasting impression of pure Colombian elegance.";
    }
    else if (titleLower.includes('ethiopia') || titleLower.includes('ethiopian')) {
      return "Experience bright floral notes with hints of citrus and berries. This fresh, mold free Ethiopian coffee offers a vibrant, complex profile with a clean, wine-like acidity and a silky smooth finish.";
    }
    else if (titleLower.includes('guatemala') || titleLower.includes('guatemalan')) {
      return "Enjoy a balanced cup with notes of chocolate, caramel, and subtle spice. This Guatemalan coffee delivers a medium body with pleasant acidity and a smooth, lingering finish.";
    }
    else if (titleLower.includes('sumatra') || titleLower.includes('sumatran')) {
      return "Indulge in earthy, herbal flavors with notes of dark chocolate and a hint of spice. This full-bodied Sumatran coffee offers low acidity with a rich, syrupy mouthfeel.";
    }
    else if (titleLower.includes('brazil') || titleLower.includes('brazilian')) {
      return "Delight in nutty, chocolate notes with a hint of caramel sweetness. This Brazilian coffee provides a smooth, medium body with low acidity and a clean finish.";
    }
    else if (titleLower.includes('costa rica') || titleLower.includes('costa rican')) {
      return "Experience bright citrus notes balanced with honey sweetness. This Costa Rican coffee offers a clean, well-balanced cup with medium body and crisp acidity.";
    }
    else if (titleLower.includes('dark roast')) {
      return "Bold and robust with notes of dark chocolate and caramelized sugar. This dark roast delivers a full body with a smooth, lingering finish and minimal acidity.";
    }
    else if (titleLower.includes('medium roast')) {
      return "Perfectly balanced with notes of caramel, nuts, and subtle fruit. This medium roast offers a smooth body with pleasant acidity and a clean, satisfying finish.";
    }
    else if (titleLower.includes('light roast')) {
      return "Bright and lively with floral notes and citrus highlights. This light roast showcases the coffee's natural flavors with a crisp acidity and delicate body.";
    }
    else if (titleLower.includes('espresso')) {
      return "Rich and intense with notes of dark chocolate and caramel. This espresso blend creates a velvety crema and delivers a bold, complex flavor profile perfect for espresso drinks.";
    }
    else if (titleLower.includes('decaf')) {
      return "All the flavor without the caffeine. This fresh, low acid, mold free decaf coffee maintains its rich, satisfying taste with notes of chocolate and nuts, offering a smooth body and balanced finish.";
    }
    else {
      return "Experience the exceptional quality of our premium, fresh, low acid, mold free coffee, carefully sourced and roasted to perfection. Each cup delivers a rich, balanced flavor profile that true coffee lovers will appreciate.";
    }
  };

  const customDescription = getCustomDescription(product.title);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
      <div
        className="relative bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
          aria-label="Close quickview"
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <div className="flex flex-col md:flex-row">
          {/* Product Image */}
          <div className="md:w-1/2 p-6">
            {image ? (
              <Image
                alt={image.altText || product.title}
                aspectRatio="1/1"
                data={image}
                sizes="(min-width: 1024px) 33vw, (min-width: 768px) 50vw, 100vw"
                className="w-full h-auto object-contain rounded-lg"
              />
            ) : (
              <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                <svg className="w-16 h-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="md:w-1/2 p-6 flex flex-col">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">{product.title}</h2>
            <p className="text-gray-500 mb-4">{product.vendor}</p>

            {/* Price */}
            <div className="mb-6">
              <div className="flex items-center">
                <span className="text-xl font-bold text-gray-900">
                  {price && <Money data={price} />}
                </span>
              </div>
            </div>

            {/* Product Description */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">About This Coffee</h3>
              <div className="prose prose-sm text-gray-700">
                <p className="leading-relaxed">{customDescription}</p>
              </div>
            </div>

            {/* View Full Details Link */}
            <div className="mt-auto pt-4 border-t border-gray-100">
              <Link
                to={`/products/${product.handle}`}
                className="block text-center py-3 px-6 bg-[#1a2333] text-white rounded-lg font-medium hover:bg-[#2a3343] transition-colors"
                onClick={onClose}
              >
                <span className="text-white">View Full Details</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
