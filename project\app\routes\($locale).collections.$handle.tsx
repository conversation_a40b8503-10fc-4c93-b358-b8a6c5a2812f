import {redirect, type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {useLoaderData, type MetaFunction, useSearchParams, useNavigate} from '@remix-run/react';
import {getPaginationVariables, Analytics} from '@shopify/hydrogen';
import {redirectIfHandleIsLocalized} from '~/lib/redirect';
import {ProductFilters} from '~/components/ProductFilters';
import {ProductCard} from '~/components/ProductCard';
import {useEffect, useState} from 'react';

export const meta: MetaFunction<typeof loader> = ({data}) => {
  return [
    {title: `${data?.collection.title ?? 'Coffee'} | The Good Coffee Company`},
    {description: data?.collection.description || 'Premium coffee products from The Good Coffee Company'}
  ];
};

export async function loader(args: LoaderFunctionArgs) {
  const {handle} = args.params;
  const {storefront} = args.context;
  const paginationVariables = getPaginationVariables(args.request, {
    pageBy: 8,
  });

  if (!handle) {
    throw redirect('/collections');
  }

  try {
    const [{collection}] = await Promise.all([
      storefront.query(COLLECTION_QUERY, {
        variables: {handle, ...paginationVariables},
      }),
    ]);

    if (!collection) {
      throw new Response(`Collection ${handle} not found`, {
        status: 404,
      });
    }

    // The API handle might be localized, so redirect to the localized handle
    redirectIfHandleIsLocalized(args.request, {handle, data: collection});

    return {
      collection,
    };
  } catch (error) {
    console.error('Collection loader error:', error);
    throw new Response('Error loading collection', {
      status: 500,
    });
  }
}

export default function Collection() {
  const {collection} = useLoaderData<typeof loader>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [filteredProducts, setFilteredProducts] = useState(collection.products.nodes);

  // Get filter values from URL params
  const coffeeType = searchParams.get('type') || 'all';
  const roast = searchParams.get('roast') || 'all';
  const origin = searchParams.get('origin') || '';

  // Filter products when search params change
  useEffect(() => {
    try {
      // This is a simple client-side filter
      const filtered = collection.products.nodes.filter(product => {
        // Helper function to check if a product matches a filter value (in title or tags)
        const productMatches = (product, value) => {
          if (!value || value === 'all') return true;

          // Check title
          if (product.title?.toLowerCase().includes(value.toLowerCase())) {
            return true;
          }

          // Check tags - convert to lowercase for case-insensitive comparison
          if (product.tags && Array.isArray(product.tags)) {
            // Debug logging to see what tags are available
            console.log('Product tags for', product.title, ':', product.tags);

            // More flexible matching - check if any tag contains the value as a substring
            return product.tags.some(tag =>
              tag.toLowerCase().includes(value.toLowerCase())
            );
          }

          return false;
        };

        // For debugging - log the product and its tags
        console.log('Filtering product:', product.title, 'Tags:', product.tags);

        // For now, return all products to debug the issue
        // Comment this out after debugging
        // return true;

        // Filter by coffee type
        if (coffeeType !== 'all') {
          if (!productMatches(product, coffeeType)) {
            return false;
          }
        }

        // Filter by roast level
        if (roast !== 'all') {
          if (!productMatches(product, roast)) {
            return false;
          }
        }

        // Filter by origin
        if (origin) {
          if (!productMatches(product, origin)) {
            return false;
          }
        }

        return true;
      });

      // If no products match the filters but there are products in the collection,
      // and filters are applied, show a more helpful message in the console
      if (filtered.length === 0 && collection.products.nodes.length > 0 &&
          (coffeeType !== 'all' || roast !== 'all' || origin)) {
        console.warn('No products match the current filters. Applied filters:',
          { coffeeType, roast, origin });
        console.log('Available products and their tags:');
        collection.products.nodes.forEach(product => {
          console.log(`${product.title}:`, product.tags);
        });
      }

      setFilteredProducts(filtered);
    } catch (error) {
      console.error('Error filtering products:', error);
      // In case of error, show all products
      setFilteredProducts(collection.products.nodes);
    }
  }, [searchParams, collection.products.nodes, coffeeType, roast, origin]);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 bg-gray-50">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-medium text-gray-900">{collection.title}</h1>
        <p className="text-sm text-gray-600">
          {filteredProducts.length} products
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar filter column */}
        <div className="lg:col-span-1">
          <ProductFilters />
        </div>

        {/* Product grid column */}
        <div className="lg:col-span-3">
          {/* Sort dropdown */}
          <div className="flex justify-end mb-6">
            <div className="inline-flex items-center">
              <span className="text-sm text-gray-600 mr-2">Sort:</span>
              <select
                className="pl-3 pr-10 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                defaultValue="featured"
              >
                <option value="featured">Featured</option>
                <option value="newest">Newest</option>
                <option value="price-asc">Price: Low to High</option>
                <option value="price-desc">Price: High to Low</option>
              </select>
            </div>
          </div>

          {/* Product grid */}
          {filteredProducts.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 px-4 border border-gray-200 rounded-lg bg-white">
              <svg className="h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
              <p className="text-sm text-gray-500 text-center">Try changing or clearing your filters.</p>
              <button
                onClick={() => navigate('?')}
                className="mt-4 px-4 py-2 bg-gray-100 border border-gray-300 rounded-md text-sm font-medium text-gray-600 hover:bg-gray-200 transition-colors"
              >
                Clear All Filters
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredProducts.map((product) => (
                <ProductCard key={product.id} product={product} loading="lazy" />
              ))}
            </div>
          )}

          {/* Load more button */}
          {collection.products.pageInfo.hasNextPage && (
            <div className="mt-8 text-center">
              <button
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
              >
                Load more
              </button>
            </div>
          )}
        </div>
      </div>

      <Analytics.CollectionView
        data={{
          collection: {
            id: collection.id,
            handle: collection.handle,
          },
        }}
      />
    </div>
  );
}

const PRODUCT_ITEM_FRAGMENT = `#graphql
  fragment MoneyProductItem on MoneyV2 {
    amount
    currencyCode
  }
  fragment ProductItem on Product {
    id
    handle
    title
    tags
    featuredImage {
      id
      altText
      url
      width
      height
    }
    priceRange {
      minVariantPrice {
        ...MoneyProductItem
      }
      maxVariantPrice {
        ...MoneyProductItem
      }
    }
  }
` as const;

const COLLECTION_QUERY = `#graphql
  ${PRODUCT_ITEM_FRAGMENT}
  query Collection(
    $handle: String!
    $country: CountryCode
    $language: LanguageCode
    $first: Int
    $last: Int
    $startCursor: String
    $endCursor: String
  ) @inContext(country: $country, language: $language) {
    collection(handle: $handle) {
      id
      handle
      title
      description
      products(
        first: $first,
        last: $last,
        before: $startCursor,
        after: $endCursor
      ) {
        nodes {
          ...ProductItem
        }
        pageInfo {
          hasPreviousPage
          hasNextPage
          endCursor
          startCursor
        }
      }
    }
  }
` as const;