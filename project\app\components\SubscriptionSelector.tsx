import {useState, useEffect} from 'react';
import type {ProductFragment, ProductVariantFragment} from 'storefrontapi.generated';

export type SellingPlanFragment = {
  id: string;
  name: string;
  description?: string | null;
  options?: Array<{
    name: string;
    value: string;
  }> | null;
};

export function SubscriptionSelector({
  product,
  selectedVariant,
  onSellingPlanChange,
}: {
  product: ProductFragment;
  selectedVariant: ProductVariantFragment;
  onSellingPlanChange: (sellingPlanId: string | null) => void;
}) {
  const [sellingPlanGroups, setSellingPlanGroups] = useState<any[]>([]);
  const [selectedSellingPlan, setSelectedSellingPlan] = useState<SellingPlanFragment | null>(null);
  const [isSubscription, setIsSubscription] = useState(product?.requiresSellingPlan || false);

  useEffect(() => {
    if (product?.sellingPlanGroups?.nodes) {
      setSellingPlanGroups(product.sellingPlanGroups.nodes);
    }
  }, [product]);

  useEffect(() => {
    if (isSubscription && sellingPlanGroups.length > 0 && sellingPlanGroups[0].sellingPlans.nodes.length > 0) {
      const plan = sellingPlanGroups[0].sellingPlans.nodes[0];
      setSelectedSellingPlan(plan);
      onSellingPlanChange(plan.id);
    } else {
      setSelectedSellingPlan(null);
      onSellingPlanChange(null);
    }
  }, [isSubscription, sellingPlanGroups, onSellingPlanChange]);

  if (!product || !selectedVariant || sellingPlanGroups.length === 0) {
    return null;
  }

  // If product requires selling plan, don't show one-time purchase option
  const showOneTimePurchase = !product.requiresSellingPlan;

  const handleSellingPlanChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const planId = e.target.value;
    const plan = sellingPlanGroups[0].sellingPlans.nodes.find(
      (plan: SellingPlanFragment) => plan.id === planId
    );
    setSelectedSellingPlan(plan);
    onSellingPlanChange(planId);
  };

  const getSubscriptionPrice = () => {
    if (!selectedVariant || !selectedSellingPlan) return null;
    
    const allocation = selectedVariant.sellingPlanAllocations?.nodes.find(
      (allocation) => allocation.sellingPlan.id === selectedSellingPlan.id
    );
    
    if (allocation && allocation.priceAdjustments[0]) {
      return formatPrice(allocation.priceAdjustments[0].price);
    }
    
    return formatPrice(selectedVariant.price);
  };

  const formatPrice = (price: {amount: string; currencyCode: string}) => {
    return `$${parseFloat(price.amount).toFixed(2)} ${price.currencyCode}`;
  };

  return (
    <div className="mt-6 space-y-4">
      <h3 className="text-sm font-medium text-gray-900">Purchase options</h3>
      
      <div className="space-y-4">
        {showOneTimePurchase && (
          <div className="flex items-center">
            <input
              id="one-time-purchase"
              name="purchase-option"
              type="radio"
              checked={!isSubscription}
              onChange={() => setIsSubscription(false)}
              className="h-4 w-4 border-gray-300 text-[#1a2333] focus:ring-[#1a2333]"
            />
            <label htmlFor="one-time-purchase" className="ml-3 text-sm text-gray-700">
              One-time purchase - {formatPrice(selectedVariant.price)}
            </label>
          </div>
        )}
        
        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="subscription"
              name="purchase-option"
              type="radio"
              checked={isSubscription}
              onChange={() => setIsSubscription(true)}
              className="h-4 w-4 border-gray-300 text-[#1a2333] focus:ring-[#1a2333]"
            />
          </div>
          <div className="ml-3 text-sm">
            <label htmlFor="subscription" className="font-medium text-gray-700">
              Subscribe - {getSubscriptionPrice()}
            </label>
            <p className="text-gray-500">Save and never run out of coffee</p>
            
            {isSubscription && sellingPlanGroups[0]?.sellingPlans?.nodes?.length > 0 && (
              <div className="mt-3">
                <select
                  value={selectedSellingPlan?.id || ''}
                  onChange={handleSellingPlanChange}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-[#1a2333] focus:border-[#1a2333] sm:text-sm rounded-md"
                >
                  {sellingPlanGroups[0].sellingPlans.nodes.map((plan: SellingPlanFragment) => (
                    <option key={plan.id} value={plan.id}>
                      {plan.name}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
