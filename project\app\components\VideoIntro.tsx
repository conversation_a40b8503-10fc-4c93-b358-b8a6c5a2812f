import { useState, useEffect, useRef } from 'react';

interface VideoIntroProps {
  onVideoEnd: () => void;
  onSkip: () => void;
}

export function VideoIntro({ onVideoEnd, onSkip }: VideoIntroProps) {
  const [showSkip, setShowSkip] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    // Detect mobile device and skip video immediately
    const userAgent = navigator.userAgent.toLowerCase();
    const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/.test(userAgent);
    setIsMobile(isMobileDevice);

    if (isMobileDevice) {
      // Skip video immediately on mobile
      setTimeout(() => {
        handleSkip();
      }, 500);
      return;
    }

    // Desktop: Show skip button after 2 seconds
    const skipTimer = setTimeout(() => {
      setShowSkip(true);
    }, 2000);

    return () => clearTimeout(skipTimer);
  }, []);

  const handleVideoEnd = () => {
    // Start fadeout animation
    setIsVisible(false);
    // Immediately call parent - let parent handle timing
    onVideoEnd();
  };

  const handleSkip = () => {
    // Start fadeout animation
    setIsVisible(false);
    // Immediately call parent - let parent handle timing
    onSkip();
  };

  const handleVideoLoad = () => {
    // Video loaded successfully
  };

  const handleVideoError = () => {
    console.log('Video failed to load, skipping to homepage');
    // Auto-skip on error
    setTimeout(() => {
      handleSkip();
    }, 1000);
  };

  const handleCanPlay = () => {
    // Video can play
    // Try to play the video manually on mobile
    if (isMobile && videoRef.current) {
      const playPromise = videoRef.current.play();
      if (playPromise !== undefined) {
        playPromise.catch((error) => {
          console.log('Mobile autoplay prevented:', error);
          // Show a play button or auto-skip
          setShowSkip(true);
        });
      }
    }
  };

  // Don't render anything on mobile - just skip
  if (isMobile) {
    return null;
  }

  return (
    <div
      className={`fixed inset-0 z-50 bg-black transition-opacity duration-1000 ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
    >
      {/* Video element */}
      <video
        ref={videoRef}
        className="w-full h-full"
        style={{
          objectFit: 'contain',
          transform: 'scale(0.95)',
          transformOrigin: 'center center'
        }}
        autoPlay
        muted
        playsInline
        onLoadedData={handleVideoLoad}
        onEnded={handleVideoEnd}
        onError={handleVideoError}
        onCanPlay={handleCanPlay}
      >
        <source src="/hero_vid.mp4" type="video/mp4" />
      </video>

      {/* Skip button */}
      {showSkip && (
        <button
          onClick={handleSkip}
          className={`absolute top-6 right-6 bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg
            hover:bg-opacity-70 transition-all duration-300 transform hover:scale-105
            ${isVisible ? 'opacity-100' : 'opacity-0'}
          `}
        >
          Skip
        </button>
      )}
    </div>
  );
}
