import type {CartLineUpdateInput} from '@shopify/hydrogen/storefront-api-types';
import type {CartLayout} from '~/components/CartMain';
import {CartForm, Image, type OptimisticCartLine} from '@shopify/hydrogen';
import {useVariantUrl} from '~/lib/variants';
import {Link} from '@remix-run/react';
import {ProductPrice} from './ProductPrice';
import {useAside} from './Aside';
import type {CartApiQueryFragment} from 'storefrontapi.generated';

type CartLine = OptimisticCartLine<CartApiQueryFragment>;

/**
 * A single line item in the cart. It displays the product image, title, price.
 * It also provides controls to update the quantity or remove the line item.
 */
export function CartLineItem({
  layout,
  line,
}: {
  layout: CartLayout;
  line: CartLine;
}) {
  const {id, merchandise} = line;
  const {product, title, image, selectedOptions} = merchandise;
  const lineItemUrl = useVariantUrl(product.handle, selectedOptions);
  const {close} = useAside();

  return (
    <li key={id} className={`py-6 flex ${layout === 'aside' ? 'py-6' : 'py-4'}`}>
      {/* Product Image */}
      <div className={`flex-shrink-0 ${layout === 'aside' ? 'w-24 h-24' : 'w-20 h-20'} rounded-md overflow-hidden border border-gray-200`}>
        {image ? (
          <Image
            alt={title}
            aspectRatio="1/1"
            data={image}
            height={layout === 'aside' ? 96 : 80}
            loading="lazy"
            width={layout === 'aside' ? 96 : 80}
            className="w-full h-full object-center object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gray-100 flex items-center justify-center">
            <svg style={{width: 'var(--icon-size-lg)', height: 'var(--icon-size-lg)'}} className="text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        )}
      </div>

      {/* Product Details */}
      <div className="ml-4 flex-1 flex flex-col">
        <div>
          <div className="flex justify-between">
            <div>
              <Link
                prefetch="intent"
                to={lineItemUrl}
                onClick={() => {
                  if (layout === 'aside') {
                    close();
                  }
                }}
                className={`${layout === 'aside' ? 'text-base' : 'text-sm'} font-medium text-gray-900 hover:text-[#1a2333] transition-colors`}
              >
                {product.title}
              </Link>
              <div className="mt-1">
                <ProductPrice price={line?.cost?.totalAmount} className={layout === 'aside' ? 'text-lg font-semibold' : ''} />
              </div>
            </div>

            {/* Remove Button - Mobile */}
            <div className="ml-4 md:hidden">
              <CartLineRemoveButton
                lineIds={[id]}
                disabled={!!line.isOptimistic}
              />
            </div>
          </div>

          {/* Product Options */}
          <div className="mt-1 text-sm text-gray-500">
            {selectedOptions.map((option) => (
              <span key={option.name} className="mr-2">
                {option.name}: {option.value}
              </span>
            ))}
          </div>
        </div>

        <div className="mt-4 flex-1 flex items-end justify-between">
          {/* Quantity Controls */}
          <CartLineQuantity line={line} />

          {/* Remove Button - Desktop */}
          <div className="hidden md:block">
            <CartLineRemoveButton
              lineIds={[id]}
              disabled={!!line.isOptimistic}
            />
          </div>
        </div>
      </div>
    </li>
  );
}

/**
 * Provides the controls to update the quantity of a line item in the cart.
 * These controls are disabled when the line item is new, and the server
 * hasn't yet responded that it was successfully added to the cart.
 */
function CartLineQuantity({line}: {line: CartLine}) {
  if (!line || typeof line?.quantity === 'undefined') return null;
  const {id: lineId, quantity, isOptimistic} = line;
  const prevQuantity = Number(Math.max(0, quantity - 1).toFixed(0));
  const nextQuantity = Number((quantity + 1).toFixed(0));

  return (
    <div className="flex items-center">
      <label className="text-sm text-gray-700 mr-3">Qty:</label>
      <div className="flex items-center border border-gray-300 rounded-md">
        <CartLineUpdateButton lines={[{id: lineId, quantity: prevQuantity}]}>
          <button
            aria-label="Decrease quantity"
            disabled={quantity <= 1 || !!isOptimistic}
            name="decrease-quantity"
            value={prevQuantity}
            className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span className="text-lg">−</span>
          </button>
        </CartLineUpdateButton>

        <span className="w-8 text-center text-sm font-medium text-gray-900">
          {isOptimistic ? (
            <div className="w-4 h-4 mx-auto rounded-full border-2 border-primary-600 border-t-transparent animate-spin"></div>
          ) : (
            quantity
          )}
        </span>

        <CartLineUpdateButton lines={[{id: lineId, quantity: nextQuantity}]}>
          <button
            aria-label="Increase quantity"
            name="increase-quantity"
            value={nextQuantity}
            disabled={!!isOptimistic}
            className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span className="text-lg">+</span>
          </button>
        </CartLineUpdateButton>
      </div>
    </div>
  );
}

/**
 * A button that removes a line item from the cart. It is disabled
 * when the line item is new, and the server hasn't yet responded
 * that it was successfully added to the cart.
 */
function CartLineRemoveButton({
  lineIds,
  disabled,
}: {
  lineIds: string[];
  disabled: boolean;
}) {
  return (
    <CartForm
      fetcherKey={getUpdateKey(lineIds)}
      route="/cart"
      action={CartForm.ACTIONS.LinesRemove}
      inputs={{lineIds}}
    >
      {(fetcher: FetcherWithComponents<any>) => (
        <button
          disabled={disabled || fetcher.state !== 'idle'}
          type="submit"
          className="text-sm font-medium text-gray-500 hover:text-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {fetcher.state !== 'idle' ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Removing...
            </span>
          ) : (
            "Remove"
          )}
        </button>
      )}
    </CartForm>
  );
}

function CartLineUpdateButton({
  children,
  lines,
}: {
  children: React.ReactNode;
  lines: CartLineUpdateInput[];
}) {
  const lineIds = lines.map((line) => line.id);

  return (
    <CartForm
      fetcherKey={getUpdateKey(lineIds)}
      route="/cart"
      action={CartForm.ACTIONS.LinesUpdate}
      inputs={{lines}}
    >
      {children}
    </CartForm>
  );
}

/**
 * Returns a unique key for the update action. This is used to make sure actions modifying the same line
 * items are not run concurrently, but cancel each other. For example, if the user clicks "Increase quantity"
 * and "Decrease quantity" in rapid succession, the actions will cancel each other and only the last one will run.
 * @param lineIds - line ids affected by the update
 * @returns
 */
function getUpdateKey(lineIds: string[]) {
  return [CartForm.ACTIONS.LinesUpdate, ...lineIds].join('-');
}
