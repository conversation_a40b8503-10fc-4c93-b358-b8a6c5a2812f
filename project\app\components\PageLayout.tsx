import {Await, Link, useLocation} from '@remix-run/react';
import {Suspense, useId} from 'react';
import type {
  CartApiQueryFragment,
  FooterQuery,
  HeaderQuery,
} from 'storefrontapi.generated';
import {Aside} from '~/components/Aside';
import {Footer} from '~/components/Footer';
import {Header, HeaderMenu} from '~/components/Header';
import {CartMain} from '~/components/CartMain';
import {
  SEARCH_ENDPOINT,
  SearchFormPredictive,
} from '~/components/SearchFormPredictive';
import {SearchResultsPredictive} from '~/components/SearchResultsPredictive';

interface PageLayoutProps {
  cart: Promise<CartApiQueryFragment | null>;
  footer: Promise<FooterQuery | null>;
  header: HeaderQuery;
  isLoggedIn: Promise<boolean>;
  publicStoreDomain: string;
  children?: React.ReactNode;
}

export function PageLayout({
  cart,
  children = null,
  footer,
  header,
  isLoggedIn,
  publicStoreDomain,
}: PageLayoutProps) {
  const location = useLocation();
  return (
    <Aside.Provider>
      <CartAside cart={cart} />
      <SearchAside />
      <MobileMenuAside header={header} publicStoreDomain={publicStoreDomain} />
      {header && (
        <Header
          header={header}
          cart={cart}
          isLoggedIn={isLoggedIn}
          publicStoreDomain={publicStoreDomain}
        />
      )}
      <main className="pt-24 md:pt-28" key={location.pathname}>{children}</main>
      <Footer
        footer={footer}
        header={header}
        publicStoreDomain={publicStoreDomain}
      />
    </Aside.Provider>
  );
}

function CartAside({cart}: {cart: PageLayoutProps['cart']}) {
  return (
    <Aside type="cart" heading="YOUR CART">
      <Suspense fallback={
        <div className="flex flex-col items-center justify-center p-8 h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mb-4"></div>
          <p className="text-gray-500">Loading your cart...</p>
        </div>
      }>
        <Await resolve={cart}>
          {(cart) => {
            return <CartMain cart={cart} layout="aside" />;
          }}
        </Await>
      </Suspense>
    </Aside>
  );
}

function SearchAside() {
  const queriesDatalistId = useId();
  return (
    <Aside type="search" heading="SEARCH">
      <div className="predictive-search p-5">
        <div className="predictive-search-form">
          <SearchFormPredictive>
            {({fetchResults, goToSearch, inputRef}) => (
              <div className="relative">
                <input
                  name="q"
                  onChange={fetchResults}
                  onFocus={fetchResults}
                  placeholder="Search for products, collections, and more..."
                  ref={inputRef}
                  type="search"
                  list={queriesDatalistId}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#1a2333] focus:border-[#1a2333] transition-all duration-300 text-base"
                  autoComplete="off"
                />
                <button
                  onClick={goToSearch}
                  className="mt-3 w-full bg-[#1a2333] text-white py-2.5 px-4 rounded-lg hover:bg-[#2a3343] transition-colors duration-300 flex items-center justify-center text-base font-medium"
                >
                  <span>Search</span>
                  <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </button>
              </div>
            )}
          </SearchFormPredictive>
        </div>

        <div className="mt-6">
          <SearchResultsPredictive>
            {({items, total, term, state, closeSearch}) => {
              const {articles, collections, pages, products, queries} = items;

              if (state === 'loading' && term.current) {
                return (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  </div>
                );
              }

              if (!total) {
                return <SearchResultsPredictive.Empty term={term} />;
              }

              return (
                <div className="space-y-8">
                  {queries.length > 0 && (
                    <div className="mb-6">
                      <h3 className="text-sm font-medium text-gray-500 mb-3 uppercase tracking-wider">Suggested Searches</h3>
                      <SearchResultsPredictive.Queries
                        queries={queries}
                        queriesDatalistId={queriesDatalistId}
                      />
                    </div>
                  )}

                  {products.length > 0 && (
                    <div className="mb-8">
                      <h3 className="text-sm font-medium text-gray-500 mb-4 uppercase tracking-wider">Products</h3>
                      <div className="grid grid-cols-1 gap-4">
                        <SearchResultsPredictive.Products
                          products={products}
                          closeSearch={closeSearch}
                          term={term}
                        />
                      </div>
                    </div>
                  )}

                  {collections.length > 0 && (
                    <div className="mb-6">
                      <h3 className="text-sm font-medium text-gray-500 mb-3 uppercase tracking-wider">Collections</h3>
                      <SearchResultsPredictive.Collections
                        collections={collections}
                        closeSearch={closeSearch}
                        term={term}
                      />
                    </div>
                  )}

                  {(pages.length > 0 || articles.length > 0) && (
                    <div className="mb-6">
                      <h3 className="text-sm font-medium text-gray-500 mb-3 uppercase tracking-wider">Pages & Articles</h3>
                      <div className="space-y-3">
                        <SearchResultsPredictive.Pages
                          pages={pages}
                          closeSearch={closeSearch}
                          term={term}
                        />
                        <SearchResultsPredictive.Articles
                          articles={articles}
                          closeSearch={closeSearch}
                          term={term}
                        />
                      </div>
                    </div>
                  )}

                  {term.current && total ? (
                    <div className="pt-5 border-t border-gray-200">
                      <Link
                        onClick={closeSearch}
                        to={`${SEARCH_ENDPOINT}?q=${term.current}`}
                        className="inline-flex items-center text-[#1a2333] hover:text-[#2a3343] font-medium text-lg"
                      >
                        View all results for "{term.current}"
                        <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </Link>
                    </div>
                  ) : null}
                </div>
              );
            }}
          </SearchResultsPredictive>
        </div>
      </div>
    </Aside>
  );
}

function MobileMenuAside({
  header,
  publicStoreDomain,
}: {
  header: PageLayoutProps['header'];
  publicStoreDomain: PageLayoutProps['publicStoreDomain'];
}) {
  return (
    header.menu &&
    header.shop.primaryDomain?.url && (
      <Aside type="mobile" heading="MENU">
        <HeaderMenu
          menu={header.menu}
          viewport="mobile"
          primaryDomainUrl={header.shop.primaryDomain.url}
          publicStoreDomain={publicStoreDomain}
        />
      </Aside>
    )
  );
}
