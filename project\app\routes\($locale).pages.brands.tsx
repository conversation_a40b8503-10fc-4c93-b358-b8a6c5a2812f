/**
 * BRANDS PAGE - TEMPORARILY DEACTIVATED
 *
 * This page is currently deactivated and will redirect to the homepage.
 * The page content is preserved for future use when we're ready to launch the Brands feature.
 * To reactivate:
 * 1. Remove the loader function that redirects to homepage
 * 2. Uncomment the Brands links in Header.tsx (mobile and desktop sections)
 */

import { type MetaFunction } from '@remix-run/react';
import { redirect } from '@shopify/remix-oxygen';

// Temporarily redirect to homepage
export async function loader() {
  return redirect('/');
}

export const meta: MetaFunction = () => {
  return [
    { title: 'Our Brands | The Good Coffee Company' },
    { description: 'Discover our family of coffee brands, each with their own unique story and flavor profile.' }
  ];
};

export default function BrandsPage() {
  return (
    <div className="bg-gray-50">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-[#1a2333] text-white">
        <div className="absolute inset-0 z-0 opacity-20">
          <img
            src="/medium_pretty_bag.webp"
            alt="Coffee brands background"
            className="w-full h-full object-cover object-center"
          />
        </div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 md:py-32">
          <div className="max-w-3xl">
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6">Our Coffee Brands</h1>
            <p className="text-base sm:text-lg md:text-xl text-gray-300 mb-6 sm:mb-8">
              We partner with exceptional coffee brands that share our commitment to quality, sustainability, and ethical sourcing.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-none">
          {/* Brands Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {/* Big River Coffee */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 transition-all duration-300 hover:shadow-lg">
              <div className="p-6 flex flex-col h-full">
                <div className="flex justify-center mb-6">
                  <img
                    src="/Big-River-Coffee-logo.jpg"
                    alt="Big River Coffee Logo"
                    className="h-40 w-auto object-contain"
                  />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-3 text-center">Big River Coffee</h2>
                <p className="text-gray-600 mb-6 text-center">
                  Exceptional coffee with a focus on sustainability and ethical sourcing practices.
                </p>
                <div className="flex-grow"></div>
                <div className="flex justify-center mt-4">
                  <a
                    href="https://bigrivercoffee.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-8 py-4 rounded-lg font-medium bg-[#d1d5db] text-gray-700 hover:bg-[#9ca3af] transition-all duration-300 shadow-sm"
                  >
                    Visit Big River Coffee
                    <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            {/* Placeholder for future brands */}
            <div className="bg-gray-50 rounded-xl border border-dashed border-gray-300 flex flex-col items-center justify-center p-8 text-center">
              <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="text-xl font-medium text-gray-500 mb-2">More Brands Coming Soon</h3>
              <p className="text-gray-400">
                We're always looking to partner with exceptional coffee brands that share our values.
              </p>
            </div>
          </div>

          {/* Partnership CTA */}
          <div className="bg-[#1a2333] rounded-xl overflow-hidden shadow-lg">
            <div className="md:flex">
              <div className="md:w-2/3 p-8 md:p-12 flex flex-col">
                <div>
                  <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">Partner With Us</h2>
                  <p className="text-gray-300 mb-10">
                    Are you a coffee brand looking to expand your reach? We're always interested in partnering with exceptional coffee brands that share our commitment to quality, sustainability, and ethical sourcing.
                  </p>
                </div>
                <div className="mt-auto">
                  <a
                    href="/pages/contact"
                    className="inline-flex items-center px-8 py-4 rounded-lg font-medium bg-[#d1d5db] text-gray-700 hover:bg-[#9ca3af] transition-all duration-300 shadow-sm"
                  >
                    Get in Touch
                    <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </a>
                </div>
              </div>
              <div className="md:w-1/3">
                <img
                  src="/blurry_guy_with_bag.webp"
                  alt="Coffee partnership"
                  className="w-full h-full object-cover object-right-top"
                  style={{ objectPosition: "80% center" }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
