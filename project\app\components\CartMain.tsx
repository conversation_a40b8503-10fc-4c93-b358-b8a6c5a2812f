import {useOptimisticCart, Analytics} from '@shopify/hydrogen';
import {Link} from '@remix-run/react';
import type {CartApiQueryFragment} from 'storefrontapi.generated';
import {useAside} from '~/components/Aside';
import {CartLineItem} from '~/components/CartLineItem';
import {CartSummary} from './CartSummary';

export type CartLayout = 'page' | 'aside';

export type CartMainProps = {
  cart: CartApiQueryFragment | null;
  layout: CartLayout;
};

/**
 * The main cart component that displays the cart items and summary.
 * It is used by both the /cart route and the cart aside dialog.
 */
export function CartMain({layout, cart: originalCart}: CartMainProps) {
  // The useOptimisticCart hook applies pending actions to the cart
  // so the user immediately sees feedback when they modify the cart.
  const cart = useOptimisticCart(originalCart);

  const linesCount = Boolean(cart?.lines?.nodes?.length || 0);
  const withDiscount =
    cart &&
    Boolean(cart?.discountCodes?.filter((code) => code.applicable)?.length);
  const className = `cart-main ${withDiscount ? 'with-discount' : ''}`;
  const cartHasItems = cart?.totalQuantity ? cart.totalQuantity > 0 : false;

  return (
    <>
      <div className={className}>
        <CartEmpty hidden={linesCount} layout={layout} />
        <div className="cart-details">
          {linesCount ? (
            <h2 className="text-xl font-bold text-gray-900 mb-4">Your Cart ({cart?.totalQuantity} {cart?.totalQuantity === 1 ? 'item' : 'items'})</h2>
          ) : null}

          <div aria-labelledby="cart-lines" className="mb-8">
            <ul className="divide-y divide-gray-200">
              {(cart?.lines?.nodes ?? []).map((line) => (
                <CartLineItem key={line.id} line={line} layout={layout} />
              ))}
            </ul>
          </div>

          {/* Suggested Products - only for page layout */}
          {cartHasItems && layout === 'page' && (
            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">You might also like</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-100 rounded-lg p-4 flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-md"></div>
                  <div>
                    <h4 className="text-sm font-medium">Suggested Product</h4>
                    <p className="text-sm text-gray-500">$19.99</p>
                  </div>
                </div>
                <div className="bg-gray-100 rounded-lg p-4 flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-md"></div>
                  <div>
                    <h4 className="text-sm font-medium">Suggested Product</h4>
                    <p className="text-sm text-gray-500">$24.99</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Analytics tracking for cart views */}
        {cartHasItems && (
          <Analytics.CartView />
        )}
      </div>

      {/* Cart Summary - now outside the scrollable area */}
      {cartHasItems && (
        <CartSummary cart={cart} layout={layout} />
      )}
    </>
  );
}

function CartEmpty({
  hidden = false,
  layout = 'aside',
}: {
  hidden: boolean;
  layout?: CartMainProps['layout'];
}) {
  const {close} = useAside();
  return (
    <div hidden={hidden} className="py-6 text-center">
      <div className="flex flex-col items-center">
        <svg className="w-16 h-16 text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>

        <h3 className="text-lg font-medium text-gray-900 mb-2">Your cart is empty</h3>
        <p className="text-gray-500 mb-6 max-w-md">
          Looks like you haven&rsquo;t added anything yet, let&rsquo;s get you
          started!
        </p>

        <Link
          to="/buy"
          onClick={close}
          prefetch="viewport"
          className="inline-flex items-center px-5 py-3 bg-[#1a2333] text-white rounded-lg hover:bg-[#2a3343] transition-colors duration-200 font-medium"
        >
          <span className="text-white">Continue shopping</span>
          <svg className="ml-2 w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </Link>

        {layout === 'page' && (
          <div className="mt-12 border-t border-gray-200 pt-8">
            <h4 className="text-base font-medium text-gray-900 mb-4">Popular right now</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-100 rounded-lg p-4 flex flex-col items-center">
                <div className="w-24 h-24 bg-gray-200 rounded-md mb-3"></div>
                <h5 className="text-sm font-medium">Popular Product</h5>
                <p className="text-sm text-gray-500">$19.99</p>
              </div>
              <div className="bg-gray-100 rounded-lg p-4 flex flex-col items-center">
                <div className="w-24 h-24 bg-gray-200 rounded-md mb-3"></div>
                <h5 className="text-sm font-medium">Popular Product</h5>
                <p className="text-sm text-gray-500">$24.99</p>
              </div>
              <div className="bg-gray-100 rounded-lg p-4 flex flex-col items-center">
                <div className="w-24 h-24 bg-gray-200 rounded-md mb-3"></div>
                <h5 className="text-sm font-medium">Popular Product</h5>
                <p className="text-sm text-gray-500">$29.99</p>
              </div>
              <div className="bg-gray-100 rounded-lg p-4 flex flex-col items-center">
                <div className="w-24 h-24 bg-gray-200 rounded-md mb-3"></div>
                <h5 className="text-sm font-medium">Popular Product</h5>
                <p className="text-sm text-gray-500">$34.99</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
