import { Link } from '@remix-run/react';
import { Image, Money } from '@shopify/hydrogen';
import { useState, useEffect } from 'react';
import { ProductQuickview } from './ProductQuickview';
import { useAside } from './Aside';
import { AddToCartButton } from './AddToCartButton';

type Product = {
  id: string;
  title: string;
  handle: string;
  vendor: string;
  variants: {
    nodes: Array<{
      id: string;
      availableForSale: boolean;
      price: {
        amount: string;
        currencyCode: string;
      };
      compareAtPrice?: {
        amount: string;
        currencyCode: string;
      } | null;
    }>;
  };
  featuredImage?: {
    url: string;
    altText: string;
    width: number;
    height: number;
  } | null;
  priceRange: {
    minVariantPrice: {
      amount: string;
      currencyCode: string;
    };
  };
  availableForSale: boolean;
};

type FilterType = 'all' | 'dark-roast' | 'medium-roast' | 'light-roast';

interface EnhancedProductGridProps {
  products: Product[];
  className?: string;
  title?: string;
  enableFiltering?: boolean;
  enableSorting?: boolean;
  layout?: 'grid' | 'list';
  columnsDesktop?: number;
  columnsMobile?: number;
  highlightNewest?: boolean;
}

export function EnhancedProductGrid({
  products,
  className = '',
  title,
  enableFiltering = true,
  enableSorting = true,
  layout = 'grid',
  columnsDesktop = 3,
  columnsMobile = 1,
  highlightNewest = true,
}: EnhancedProductGridProps) {
  const [activeFilter, setActiveFilter] = useState<FilterType>('all');
  const [sortOption, setSortOption] = useState<'price-asc' | 'price-desc' | 'name-asc' | 'name-desc' | 'newest'>('newest');
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(products);
  const [isLoading, setIsLoading] = useState(false);
  const [visibleItems, setVisibleItems] = useState<number[]>([]);
  const [quickviewProduct, setQuickviewProduct] = useState<Product | null>(null);
  const [isQuickviewOpen, setIsQuickviewOpen] = useState(false);
  const { open } = useAside();

  // Function to determine product type based on title
  const getProductType = (product: Product): FilterType => {
    const title = product.title.toLowerCase();
    if (title.includes('dark')) {
      return 'dark-roast';
    } else if (title.includes('light')) {
      return 'light-roast';
    } else {
      return 'medium-roast';
    }
  };

  // Function to sort products
  const sortProducts = (products: Product[]) => {
    let sorted = [...products];
    switch (sortOption) {
      case 'price-asc':
        sorted = sorted.sort((a, b) =>
          parseFloat(a.priceRange.minVariantPrice.amount) - parseFloat(b.priceRange.minVariantPrice.amount)
        );
        break;
      case 'price-desc':
        sorted = sorted.sort((a, b) =>
          parseFloat(b.priceRange.minVariantPrice.amount) - parseFloat(a.priceRange.minVariantPrice.amount)
        );
        break;
      case 'name-asc':
        sorted = sorted.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'name-desc':
        sorted = sorted.sort((a, b) => b.title.localeCompare(a.title));
        break;
      // Newest sorting uses original order, assuming products are already sorted by newest
      default:
        break;
    }
    return sorted;
  };

  // Apply filtering and sorting whenever dependencies change
  useEffect(() => {
    setIsLoading(true);
    // Reset visible items first
    setVisibleItems([]);

    // Small delay to show loading state for better UX
    const timer = setTimeout(() => {
      let result = [...products];

      // Filter out subscription products
      result = result.filter(product => !product.handle.includes('subscription'));

      // Apply filter
      if (activeFilter !== 'all') {
        result = result.filter(product => getProductType(product) === activeFilter);
      }

      // Apply sorting
      result = sortProducts(result);

      setFilteredProducts(result);
      setIsLoading(false);

      // Start the staggered reveal animation
      const revealTimer = setTimeout(() => {
        // Create an array of indices to reveal all at once
        const allIndices = result.map((_, index) => index);
        setVisibleItems(allIndices);
      }, 300);

      return () => clearTimeout(revealTimer);
    }, 300);

    return () => clearTimeout(timer);
  }, [activeFilter, sortOption, products]);

  // Modern product card classes with subtle hover effects
  const productCardClasses = "bg-white rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg group";

  // Class for badge with more elegant styling
  const getBadgeClass = (type: FilterType) => {
    switch (type) {
      case 'dark-roast':
        return 'bg-gradient-to-r from-[#3a2213] to-[#4a3223] text-white';
      case 'medium-roast':
        return 'bg-gradient-to-r from-[#8c5e2a] to-[#9c6e3a] text-white';
      case 'light-roast':
        return 'bg-gradient-to-r from-[#c8a27a] to-[#d8b28a] text-white';
      default:
        return 'bg-gradient-to-r from-primary-600 to-primary-500 text-white';
    }
  };

  // For the grid layout with improved spacing
  const getGridClasses = () => {
    const baseClasses = 'grid gap-6 md:gap-8';
    const mobileClasses = columnsMobile === 1 ? 'grid-cols-1' : 'grid-cols-2';
    const desktopClasses = columnsDesktop === 3 ? 'lg:grid-cols-3' : 'lg:grid-cols-4';

    return `${baseClasses} ${mobileClasses} sm:grid-cols-2 md:grid-cols-2 ${desktopClasses}`;
  };

  // For the list layout with improved spacing
  const listClasses = `
    flex flex-col gap-6
  `;

  return (
    <div className={`relative ${className}`}>
      {title && (
        <h2 className="text-3xl font-bold mb-6 text-gray-800">{title}</h2>
      )}

      {/* Controls container - modernized with better spacing and visual hierarchy */}
      <div className="flex flex-col md:flex-row justify-between mb-8 gap-4">
        {/* Filter buttons - redesigned with pill buttons and color indicators */}
        {enableFiltering && (
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setActiveFilter('all')}
              className={`px-5 py-2.5 rounded-full text-sm font-medium transition-all duration-200 shadow-sm ${
                activeFilter === 'all'
                  ? 'bg-gradient-to-r from-primary-600 to-primary-500 text-white shadow-md'
                  : 'bg-white text-gray-700 hover:bg-gray-50 hover:shadow'
              }`}
            >
              All Coffee
            </button>
            <button
              onClick={() => setActiveFilter('dark-roast')}
              className={`px-5 py-2.5 rounded-full text-sm font-medium transition-all duration-200 shadow-sm flex items-center ${
                activeFilter === 'dark-roast'
                  ? 'bg-gradient-to-r from-[#3a2213] to-[#4a3223] text-white shadow-md'
                  : 'bg-white text-gray-700 hover:bg-gray-50 hover:shadow'
              }`}
            >
              <span className="w-3 h-3 rounded-full bg-[#3a2213] mr-2"></span>
              Dark Roast
            </button>
            <button
              onClick={() => setActiveFilter('medium-roast')}
              className={`px-5 py-2.5 rounded-full text-sm font-medium transition-all duration-200 shadow-sm flex items-center ${
                activeFilter === 'medium-roast'
                  ? 'bg-gradient-to-r from-[#8c5e2a] to-[#9c6e3a] text-white shadow-md'
                  : 'bg-white text-gray-700 hover:bg-gray-50 hover:shadow'
              }`}
            >
              <span className="w-3 h-3 rounded-full bg-[#8c5e2a] mr-2"></span>
              Medium Roast
            </button>
            <button
              onClick={() => setActiveFilter('light-roast')}
              className={`px-5 py-2.5 rounded-full text-sm font-medium transition-all duration-200 shadow-sm flex items-center ${
                activeFilter === 'light-roast'
                  ? 'bg-gradient-to-r from-[#c8a27a] to-[#d8b28a] text-white shadow-md'
                  : 'bg-white text-gray-700 hover:bg-gray-50 hover:shadow'
              }`}
            >
              <span className="w-3 h-3 rounded-full bg-[#c8a27a] mr-2"></span>
              Light Roast
            </button>
          </div>
        )}

        {/* Sort options - redesigned with a more elegant dropdown */}
        {enableSorting && (
          <div className="relative">
            <div className="relative inline-block">
              <select
                value={sortOption}
                onChange={(e) => setSortOption(e.target.value as any)}
                className="appearance-none block w-full md:w-auto px-5 py-2.5 pr-10 rounded-lg bg-white border border-gray-200 shadow-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-primary-400 transition-all duration-200"
              >
                <option value="newest">Newest First</option>
                <option value="price-asc">Price: Low to High</option>
                <option value="price-desc">Price: High to Low</option>
                <option value="name-asc">Name: A to Z</option>
                <option value="name-desc">Name: Z to A</option>
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-gray-500">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center z-10">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      )}

      {/* Products grid/list */}
      <div className={`${layout === 'grid' ? getGridClasses() : listClasses}`}>
        {filteredProducts.length === 0 ? (
          <div className="col-span-full text-center py-12 text-gray-500">
            No products found. Try changing your filters.
          </div>
        ) : (
          filteredProducts.map((product, index) => {
            const isNewProduct = index === 0 && highlightNewest;
            const isVisible = visibleItems.includes(index);
            const productType = getProductType(product);
            const {price, compareAtPrice} = product.variants?.nodes[0] || {};
            const isDiscounted = compareAtPrice?.amount && compareAtPrice.amount > (price?.amount || '0');

            // Animation and highlight classes
            const animationClass = isVisible
              ? 'opacity-100 translate-y-0'
              : 'opacity-0 translate-y-8';

            // Apply highlight styles for newest product if enabled
            const highlightClasses = isNewProduct
              ? 'ring-2 ring-primary-100 rounded-lg shadow-md'
              : '';

            return (
              <div
                key={product.id}
                className={`
                  ${productCardClasses}
                  ${animationClass}
                  ${highlightClasses}
                  transition-all duration-500 ease-out
                  ${layout === 'list' ? 'flex gap-6 p-4 bg-white rounded-xl shadow-sm' : ''}
                `}
                style={{
                  transitionDelay: `${index * 50}ms`,
                }}
              >
                <div className={`
                  ${layout === 'grid' ? 'block' : 'flex gap-6'}
                  h-full w-full
                `}>
                  {/* Product Image with enhanced hover effects */}
                  <div className={`
                    relative overflow-hidden rounded-t-xl ${layout === 'list' ? 'rounded-l-xl rounded-tr-none' : ''}
                    ${layout === 'grid' ? 'aspect-square w-full' : 'w-1/3 min-w-32'}
                  `}>
                    <Link
                      to={`/products/${product.handle}`}
                      prefetch="intent"
                      className="group block h-full"
                    >
                      {product.featuredImage && (
                        <Image
                          alt={product.featuredImage.altText || product.title}
                          data={product.featuredImage}
                          sizes={layout === 'grid'
                            ? "(min-width: 1024px) 33vw, (min-width: 768px) 50vw, 100vw"
                            : "200px"}
                          className="h-full w-full object-cover object-center transition-all duration-700 group-hover:scale-105 group-hover:opacity-95"
                        />
                      )}

                      {/* Quick view overlay - hidden on mobile */}
                      <div
                        className="hidden md:flex absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 items-center justify-center cursor-pointer"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setQuickviewProduct(product);
                          setIsQuickviewOpen(true);
                        }}
                      >
                        <span className="bg-white text-gray-900 px-4 py-2 rounded-full text-sm font-medium shadow-md transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
                          Quick Preview
                        </span>
                      </div>
                    </Link>

                    {/* Product Badge - redesigned with gradient */}
                    <div className={`
                      absolute top-3 left-3 px-3 py-1 text-xs font-medium rounded-full shadow-sm
                      ${getBadgeClass(productType)}
                    `}>
                      {productType === 'dark-roast'
                        ? 'Dark Roast'
                        : productType === 'light-roast'
                          ? 'Light Roast'
                          : 'Medium Roast'}
                    </div>

                    {/* Out of stock overlay - improved styling */}
                    {!product.availableForSale && (
                      <div className="absolute inset-0 flex items-center justify-center bg-black/60 backdrop-blur-sm">
                        <span className="text-white font-medium px-4 py-2 bg-black/50 rounded-full border border-white/20">
                          Sold Out
                        </span>
                      </div>
                    )}

                    {/* New label with improved styling */}
                    {isNewProduct && (
                      <div className="absolute top-3 right-3 bg-gradient-to-r from-primary-600 to-primary-500 text-white text-xs font-bold uppercase px-3 py-1 rounded-full shadow-sm">
                        New!
                      </div>
                    )}
                  </div>

                  {/* Product Info - enhanced with better spacing and typography */}
                  <div className={`
                    flex flex-col p-5
                    ${layout === 'grid' ? '' : 'flex-1'}
                  `}>
                    <Link
                      to={`/products/${product.handle}`}
                      prefetch="intent"
                      className="group"
                    >
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                        {product.title}
                      </h3>
                    </Link>

                    <div className="text-sm text-gray-500 mb-3">{product.vendor}</div>

                    {/* Origin badge - improved styling */}
                    <div className="mt-1 mb-auto">
                      <span className="inline-flex items-center text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded-full">
                        <svg className="w-3 h-3 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        Origin: Various Regions
                      </span>
                    </div>

                    {/* Price area with add to cart button */}
                    <div className="mt-auto pt-3 flex flex-col gap-3">
                      <div className="flex justify-between items-center">
                        <div className="text-lg font-bold text-gray-900">
                          <Money data={price!} />
                        </div>

                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            setQuickviewProduct(product);
                            setIsQuickviewOpen(true);
                          }}
                          className="hidden md:block text-sm font-medium text-primary-600 hover:text-primary-800 transition-colors"
                        >
                          Quick View →
                        </button>
                      </div>

                      {/* Add to cart button */}
                      {layout === 'grid' && (
                        <div className="flex gap-2">
                          {product.availableForSale && product.variants?.nodes[0] ? (
                            <AddToCartButton
                              onClick={() => {
                                open('cart');
                              }}
                              lines={[
                                {
                                  merchandiseId: product.variants.nodes[0].id,
                                  quantity: 1,
                                },
                              ]}
                              className="flex-1 bg-[#1e3a8a] hover:bg-[#1e40af] text-white py-2.5 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center text-sm font-medium"
                            >
                              <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z" />
                              </svg>
                              Add to Cart
                            </AddToCartButton>
                          ) : (
                            <button
                              disabled
                              className="flex-1 bg-gray-300 text-gray-500 py-2.5 px-4 rounded-lg cursor-not-allowed text-sm font-medium"
                            >
                              Sold Out
                            </button>
                          )}

                          <Link
                            to={`/products/${product.handle}`}
                            prefetch="intent"
                            className="px-3 py-2.5 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center justify-center"
                            title="View product details"
                          >
                            <svg className="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          </Link>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Product Quickview Modal */}
      {quickviewProduct && (
        <ProductQuickview
          product={quickviewProduct}
          isOpen={isQuickviewOpen}
          onClose={() => {
            setIsQuickviewOpen(false);
            setQuickviewProduct(null);
          }}
        />
      )}

      {/* Debug output */}
      {process.env.NODE_ENV === 'development' && quickviewProduct && isQuickviewOpen && (
        <div className="hidden">
          {console.log('Product passed to quickview:', {
            product: quickviewProduct,
            description: quickviewProduct.description,
            descriptionHtml: quickviewProduct.descriptionHtml
          })}
        </div>
      )}
    </div>
  );
}
