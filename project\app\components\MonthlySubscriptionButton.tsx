import { AddToCartButton } from './AddToCartButton';
import { useAside } from './Aside';
import type { ProductFragment, ProductVariantFragment } from 'storefrontapi.generated';

interface MonthlySubscriptionButtonProps {
  product: ProductFragment;
  selectedVariant: ProductVariantFragment;
  quantity: number;
}

export function MonthlySubscriptionButton({
  product,
  selectedVariant,
  quantity
}: MonthlySubscriptionButtonProps) {
  const { open } = useAside();

  // Your monthly selling plan ID from Seal Subscriptions
  const MONTHLY_SELLING_PLAN_ID = 'gid://shopify/SellingPlan/4447502515';

  // Debug logging
  console.log('MonthlySubscriptionButton Debug:', {
    productTitle: product?.title,
    hasProduct: !!product,
    hasSellingPlanGroups: !!product?.sellingPlanGroups?.nodes?.length,
    sellingPlanCount: product?.sellingPlanGroups?.nodes?.length || 0,
    hasSelectedVariant: !!selectedVariant,
    variantAvailable: selectedVariant?.availableForSale,
    variantId: selectedVariant?.id
  });

  console.log('MonthlySubscriptionButton: Component is being called!');

  // Only show if variant is available (since all products have selling plans)
  if (!selectedVariant?.availableForSale) {
    console.log('MonthlySubscriptionButton: Hidden because variant not available');
    return null;
  }

  return (
    <div className="mb-2">
      <AddToCartButton
        onClick={() => {
          open('cart');
        }}
        lines={[
          {
            merchandiseId: selectedVariant.id,
            quantity: quantity,
            sellingPlanId: MONTHLY_SELLING_PLAN_ID,
          },
        ]}
        className="w-full bg-[#2563eb] hover:bg-[#1d4ed8] text-white py-4 px-6 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center shadow-sm"
      >
        <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        Subscribe Monthly
      </AddToCartButton>
    </div>
  );
}
