{"include": ["./**/*.d.ts", "./**/*.ts", "./**/*.tsx"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2022"], "isolatedModules": true, "esModuleInterop": true, "jsx": "react-jsx", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "module": "ES2022", "target": "ES2022", "strict": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "baseUrl": ".", "types": ["@shopify/oxygen-workers-types", "@remix-run/server-runtime", "vite/client"], "paths": {"~/*": ["app/*"]}, "noEmit": true}}