import {redirect, type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {useLoaderData, type MetaFunction} from '@remix-run/react';
import {Money, Image, flattenConnection} from '@shopify/hydrogen';
import type {OrderLineItemFullFragment} from 'customer-accountapi.generated';
import {CUSTOMER_ORDER_QUERY} from '~/graphql/customer-account/CustomerOrderQuery';

export const meta: MetaFunction<typeof loader> = ({data}) => {
  return [{title: `Order ${data?.order?.name}`}];
};

export async function loader({params, context, request}: LoaderFunctionArgs) {
  try {
    // First check authentication status
    await context.customerAccount.handleAuthStatus();

    if (!params.id) {
      return redirect('/account/orders');
    }

    const orderId = atob(params.id);
    const {data, errors} = await context.customerAccount.query(
      CUSTOMER_ORDER_QUERY,
      {
        variables: {orderId},
      },
    );

    if (errors?.length || !data?.order) {
      return redirect('/account/orders');
    }

    const {order} = data;

    const lineItems = flattenConnection(order.lineItems);
    const discountApplications = flattenConnection(order.discountApplications);

    const fulfillmentStatus =
      flattenConnection(order.fulfillments)[0]?.status ?? 'N/A';

    const firstDiscount = discountApplications[0]?.value;

    const discountValue =
      firstDiscount?.__typename === 'MoneyV2' && firstDiscount;

    const discountPercentage =
      firstDiscount?.__typename === 'PricingPercentageValue' &&
      firstDiscount?.percentage;

    return {
      order,
      lineItems,
      discountValue,
      discountPercentage,
      fulfillmentStatus,
    };
  } catch (error) {
    // If any authentication error occurs, redirect to login
    const url = new URL(request.url);
    return redirect(`/account/login?returnTo=${encodeURIComponent(url.pathname)}`);
  }
}

export default function OrderRoute() {
  const {
    order,
    lineItems,
    discountValue,
    discountPercentage,
    fulfillmentStatus,
  } = useLoaderData<typeof loader>();
  return (
    <div className="account-order">
      <div className="mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-1">Order {order.name}</h2>
            <p className="text-sm text-gray-500">Placed on {new Date(order.processedAt!).toDateString()}</p>
          </div>
          <div className="mt-4 md:mt-0">
            <a
              target="_blank"
              href={order.statusPageUrl}
              rel="noreferrer"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm !text-white bg-[#1a2333] hover:bg-[#2a3749] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1a2333]"
            >
              View Order Status
              <svg className="ml-2 -mr-1 h-4 w-4 !text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </a>
          </div>
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden mb-6">
        <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
          <h3 className="text-base font-medium text-gray-900">Order Items</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {lineItems.map((lineItem, lineItemIndex) => (
                // eslint-disable-next-line react/no-array-index-key
                <OrderLineRow key={lineItemIndex} lineItem={lineItem} />
              ))}
            </tbody>
            <tfoot className="bg-gray-50">
              {((discountValue && discountValue.amount) ||
                discountPercentage) && (
                <tr>
                  <th scope="row" colSpan={3} className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                    Discounts
                  </th>
                  <td className="px-6 py-3 text-sm text-gray-900">
                    {discountPercentage ? (
                      <span className="text-green-600 font-medium">-{discountPercentage}% OFF</span>
                    ) : (
                      discountValue && <Money className="text-green-600 font-medium" data={discountValue!} />
                    )}
                  </td>
                </tr>
              )}
              <tr>
                <th scope="row" colSpan={3} className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                  Subtotal
                </th>
                <td className="px-6 py-3 text-sm text-gray-900">
                  <Money data={order.subtotal!} />
                </td>
              </tr>
              <tr>
                <th scope="row" colSpan={3} className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                  Tax
                </th>
                <td className="px-6 py-3 text-sm text-gray-900">
                  <Money data={order.totalTax!} />
                </td>
              </tr>
              <tr>
                <th scope="row" colSpan={3} className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                  Total
                </th>
                <td className="px-6 py-3 text-sm font-bold text-gray-900">
                  <Money data={order.totalPrice!} />
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
          <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
            <h3 className="text-base font-medium text-gray-900">Shipping Address</h3>
          </div>
          <div className="px-4 py-5 sm:p-6">
            {order?.shippingAddress ? (
              <address className="not-italic">
                <p className="font-medium text-gray-900">{order.shippingAddress.name}</p>
                {order.shippingAddress.formatted ? (
                  <p className="mt-1 text-gray-600">{order.shippingAddress.formatted}</p>
                ) : (
                  ''
                )}
                {order.shippingAddress.formattedArea ? (
                  <p className="mt-1 text-gray-600">{order.shippingAddress.formattedArea}</p>
                ) : (
                  ''
                )}
              </address>
            ) : (
              <p className="text-gray-500">No shipping address defined</p>
            )}
          </div>
        </div>

        <div className="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
          <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
            <h3 className="text-base font-medium text-gray-900">Order Status</h3>
          </div>
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                ${fulfillmentStatus.toLowerCase().includes('fulfilled') ? 'bg-green-100 text-green-800' :
                  fulfillmentStatus.toLowerCase().includes('progress') ? 'bg-blue-100 text-blue-800' :
                  fulfillmentStatus.toLowerCase().includes('partial') ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'}`}>
                {fulfillmentStatus}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function OrderLineRow({lineItem}: {lineItem: OrderLineItemFullFragment}) {
  return (
    <tr key={lineItem.id}>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          {lineItem?.image && (
            <div className="flex-shrink-0 h-16 w-16 mr-4">
              <Image
                data={lineItem.image}
                width={64}
                height={64}
                className="h-16 w-16 rounded-md object-cover"
              />
            </div>
          )}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">{lineItem.title}</p>
            {lineItem.variantTitle && (
              <p className="text-xs text-gray-500 truncate">{lineItem.variantTitle}</p>
            )}
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        <Money data={lineItem.price!} />
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {lineItem.quantity}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <Money data={lineItem.totalDiscount!} />
      </td>
    </tr>
  );
}
