/**
 * <PERSON><PERSON><PERSON> to set up metaobject definitions for contact and wholesale forms
 * Run this once to create the required metaobject definitions in Shopify admin
 */

const CONTACT_FORM_DEFINITION = `
  mutation CreateContactFormDefinition {
    metaobjectDefinitionCreate(definition: {
      type: "contact_form_submission"
      name: "Contact Form Submission"
      description: "Stores contact form submissions from the website"
      fieldDefinitions: [
        {
          key: "first_name"
          name: "First Name"
          description: "Customer's first name"
          type: {
            name: "single_line_text_field"
          }
          required: true
        }
        {
          key: "last_name"
          name: "Last Name"
          description: "Customer's last name"
          type: {
            name: "single_line_text_field"
          }
          required: true
        }
        {
          key: "email"
          name: "Email"
          description: "Customer's email address"
          type: {
            name: "single_line_text_field"
          }
          required: true
        }
        {
          key: "phone"
          name: "Phone"
          description: "Customer's phone number"
          type: {
            name: "single_line_text_field"
          }
          required: false
        }
        {
          key: "subject"
          name: "Subject"
          description: "Subject of the inquiry"
          type: {
            name: "single_line_text_field"
          }
          required: true
        }
        {
          key: "message"
          name: "Message"
          description: "Customer's message"
          type: {
            name: "multi_line_text_field"
          }
          required: true
        }
        {
          key: "submitted_at"
          name: "Submitted At"
          description: "When the form was submitted"
          type: {
            name: "single_line_text_field"
          }
          required: true
        }
      ]
      access: {
        admin: MERCHANT_READ_WRITE
        storefront: NONE
      }
    }) {
      metaobjectDefinition {
        id
        type
        name
      }
      userErrors {
        field
        message
        code
      }
    }
  }
`;

const WHOLESALE_INQUIRY_DEFINITION = `
  mutation CreateWholesaleInquiryDefinition {
    metaobjectDefinitionCreate(definition: {
      type: "wholesale_inquiry"
      name: "Wholesale Inquiry"
      description: "Stores wholesale program inquiries from the website"
      fieldDefinitions: [
        {
          key: "name"
          name: "Name"
          description: "Contact person's name"
          type: {
            name: "single_line_text_field"
          }
          required: true
        }
        {
          key: "email"
          name: "Email"
          description: "Contact email address"
          type: {
            name: "single_line_text_field"
          }
          required: true
        }
        {
          key: "phone"
          name: "Phone"
          description: "Contact phone number"
          type: {
            name: "single_line_text_field"
          }
          required: false
        }
        {
          key: "company"
          name: "Company"
          description: "Company or organization name"
          type: {
            name: "single_line_text_field"
          }
          required: true
        }
        {
          key: "business_type"
          name: "Business Type"
          description: "Type of business"
          type: {
            name: "single_line_text_field"
          }
          required: true
        }
        {
          key: "message"
          name: "Message"
          description: "Additional details about their needs"
          type: {
            name: "multi_line_text_field"
          }
          required: false
        }
        {
          key: "submitted_at"
          name: "Submitted At"
          description: "When the inquiry was submitted"
          type: {
            name: "single_line_text_field"
          }
          required: true
        }
      ]
      access: {
        admin: MERCHANT_READ_WRITE
        storefront: NONE
      }
    }) {
      metaobjectDefinition {
        id
        type
        name
      }
      userErrors {
        field
        message
        code
      }
    }
  }
`;

async function setupMetaobjects() {
  console.log('Setting up metaobject definitions...');
  
  // This would need to be run in a Shopify app context or via the Admin API
  // For now, this serves as documentation of the required structure
  
  console.log('Contact Form Definition:', CONTACT_FORM_DEFINITION);
  console.log('Wholesale Inquiry Definition:', WHOLESALE_INQUIRY_DEFINITION);
}

// Export for use in Shopify app context
export { CONTACT_FORM_DEFINITION, WHOLESALE_INQUIRY_DEFINITION };
